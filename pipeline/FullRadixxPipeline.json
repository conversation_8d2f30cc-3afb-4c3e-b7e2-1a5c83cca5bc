{"name": "FullRadixxPipeline", "properties": {"activities": [{"name": "JobConfigs", "type": "Lookup", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "JsonSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFileName": "*.json", "enablePartitionDiscovery": false}, "formatSettings": {"type": "JsonReadSettings"}}, "dataset": {"referenceName": "databricksjobsconfigs", "type": "DatasetReference"}, "firstRowOnly": false}}, {"name": "SetUnityCatalogClusterIDUC", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateUnityCatalog", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "clusterID", "value": {"value": "@activity('FilterUpdateUnityCatalog').output.value[0].cluster_id", "type": "Expression"}}}, {"name": "FilterUpdateUnityCatalog", "type": "Filter", "dependsOn": [{"activity": "JobConfigs", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('JobConfigs').output.value", "type": "Expression"}, "condition": {"value": "@and(equals(item().job_name, 'UpdateUnityCatalog'), equals(item().env, variables('env')) )", "type": "Expression"}}}, {"name": "IngestRadixx", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IngestRadixx", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "ReservationsSQLQueryUpsert", "type": "SynapseNotebook", "dependsOn": [{"activity": "FilterUpdateMLReservations", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "ReservationsSQLQueryUpsert", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "UpdateUnityCatalog", "type": "DatabricksJob", "dependsOn": [{"activity": "IngestRadixx", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogClusterIDUC", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogDatabricksURLUC", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogJobIDUC", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"jobId": {"value": "@variables('jobID')", "type": "Expression"}}, "linkedServiceName": {"referenceName": "LakehouseAzureDatabricks", "type": "LinkedServiceReference", "parameters": {"keyvaultbaseURL": {"value": "@concat('https://', variables('env'), '-san-kv-lakehouse-001.vault.azure.net/')", "type": "Expression"}, "clusterID": {"value": "@variables('clusterID')", "type": "Expression"}, "databricksURL": {"value": "@variables('databricksURL')", "type": "Expression"}, "secretName": {"value": "@pipeline().parameters.databricksAccessTokenName", "type": "Expression"}}}}, {"name": "UpdateMetrics", "type": "DatabricksJob", "dependsOn": [{"activity": "SetUnityCatalogClusterIDMetrics", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogDatabricksURLMetrics", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogJobIDMetrics", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"jobId": {"value": "@variables('jobID')", "type": "Expression"}}, "linkedServiceName": {"referenceName": "LakehouseAzureDatabricks", "type": "LinkedServiceReference", "parameters": {"keyvaultbaseURL": {"value": "@concat('https://', variables('env'), '-san-kv-lakehouse-001.vault.azure.net/')", "type": "Expression"}, "clusterID": {"value": "@variables('clusterID')", "type": "Expression"}, "databricksURL": {"value": "@variables('databricksURL')", "type": "Expression"}, "secretName": {"value": "@pipeline().parameters.databricksAccessTokenName", "type": "Expression"}}}}, {"name": "UpdateMLReservations", "type": "DatabricksJob", "dependsOn": [{"activity": "ReservationsSQLQueryUpsert", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogClusterIDML", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogDatabricksURLML", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogJobIDDatabricksURL", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"jobId": {"value": "@variables('jobID')", "type": "Expression"}}, "linkedServiceName": {"referenceName": "LakehouseAzureDatabricks", "type": "LinkedServiceReference", "parameters": {"keyvaultbaseURL": {"value": "@concat('https://', variables('env'), '-san-kv-lakehouse-001.vault.azure.net/')", "type": "Expression"}, "clusterID": {"value": "@variables('clusterID')", "type": "Expression"}, "databricksURL": {"value": "@variables('databricksURL')", "type": "Expression"}, "secretName": {"value": "@pipeline().parameters.databricksAccessTokenName", "type": "Expression"}}}}, {"name": "SetUnityCatalogDatabricksURLUC", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateUnityCatalog", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "databricksURL", "value": {"value": "@activity('FilterUpdateUnityCatalog').output.value[0].databricks_url", "type": "Expression"}}}, {"name": "FilterUpdateMetrics", "type": "Filter", "dependsOn": [{"activity": "JobConfigs", "dependencyConditions": ["Succeeded"]}, {"activity": "UpdateUnityCatalog", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('JobConfigs').output.value", "type": "Expression"}, "condition": {"value": "@and(equals(item().job_name, 'UpdateMetrics'), equals(item().env, variables('env')))", "type": "Expression"}}}, {"name": "SetUnityCatalogClusterIDMetrics", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMetrics", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "clusterID", "value": {"value": "@activity('FilterUpdateMetrics').output.value[0].cluster_id", "type": "Expression"}}}, {"name": "SetUnityCatalogDatabricksURLMetrics", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMetrics", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "databricksURL", "value": {"value": "@activity('FilterUpdateMetrics').output.value[0].databricks_url", "type": "Expression"}}}, {"name": "FilterUpdateMLReservations", "type": "Filter", "dependsOn": [{"activity": "JobConfigs", "dependencyConditions": ["Succeeded"]}, {"activity": "UpdateMetrics", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('JobConfigs').output.value", "type": "Expression"}, "condition": {"value": "@and(equals(item().job_name, 'UpdateMLReservations'), equals(item().env, variables('env')) )", "type": "Expression"}}}, {"name": "SetUnityCatalogClusterIDML", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMLReservations", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "clusterID", "value": {"value": "@activity('FilterUpdateMLReservations').output.value[0].cluster_id", "type": "Expression"}}}, {"name": "SetUnityCatalogDatabricksURLML", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMLReservations", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "databricksURL", "value": {"value": "@activity('FilterUpdateMLReservations').output.value[0].databricks_url", "type": "Expression"}}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}, {"name": "SetUnityCatalogJobIDUC", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateUnityCatalog", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "jobID", "value": {"value": "@activity('FilterUpdateUnityCatalog').output.value[0].job_id", "type": "Expression"}}}, {"name": "SetUnityCatalogJobIDMetrics", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMetrics", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "jobID", "value": {"value": "@activity('FilterUpdateMetrics').output.value[0].job_id", "type": "Expression"}}}, {"name": "SetUnityCatalogJobIDDatabricksURL", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMLReservations", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "jobID", "value": {"value": "@activity('FilterUpdateMLReservations').output.value[0].job_id", "type": "Expression"}}}], "parameters": {"databricksAccessTokenName": {"type": "string", "defaultValue": "lakehouse-databricks-access-token"}}, "variables": {"databricksURL": {"type": "String", "defaultValue": "https://adb-293568896631603.3.azuredatabricks.net/"}, "clusterID": {"type": "String", "defaultValue": "0703-124105-oa6zhuqc"}, "env": {"type": "String", "defaultValue": "dev"}, "jobID": {"type": "Integer"}}, "annotations": []}}