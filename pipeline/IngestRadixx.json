{"name": "IngestRadixx", "properties": {"activities": [{"name": "WaterMarks", "type": "Lookup", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "JsonSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFileName": "*.json", "enablePartitionDiscovery": false}, "formatSettings": {"type": "JsonReadSettings"}}, "dataset": {"referenceName": "metadajsonwatermarks", "type": "DatasetReference"}, "firstRowOnly": false}}, {"name": "FetchRadixx", "type": "ForEach", "dependsOn": [{"activity": "WaterMarks", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('WaterMarks').output.value", "type": "Expression"}, "isSequential": false, "activities": [{"name": "IfExcludedTable1", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@if(contains(pipeline().parameters.tables_to_exclude, item().table_name), false, true)", "type": "Expression"}, "ifTrueActivities": [{"name": "IngestGenericRadixx", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IngestGenericRadixx", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"targetTable": {"value": "@item().table_name", "type": "Expression"}, "watermarkColumn": {"value": "@item().watermark_column_name", "type": "Expression"}, "lastWatermark": {"value": "@item().last_watermark", "type": "Expression"}}}}]}}]}}, {"name": "ParquetToDelta", "type": "ForEach", "dependsOn": [{"activity": "FetchRadixx", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('WaterMarks').output.value", "type": "Expression"}, "activities": [{"name": "IfExcludedTable2", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@if(contains(pipeline().parameters.tables_to_exclude, item().table_name), false, true)", "type": "Expression"}, "ifTrueActivities": [{"name": "GenericParquetToBronzeDelta", "type": "ExecuteDataFlow", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "GenericParquetToBronzeDelta", "type": "DataFlowReference", "parameters": {"source": {"value": "'@{item().table_name}'", "type": "Expression"}, "db": {"value": "'@{pipeline().parameters.db}'", "type": "Expression"}, "upsert_column_names": {"value": "@item().primary_key_columns", "type": "Expression"}}}, "compute": {"coreCount": 16, "computeType": "General"}, "traceLevel": "Fine"}}]}}]}}, {"name": "UpdateWatermarks", "type": "ForEach", "dependsOn": [{"activity": "ParquetToDelta", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('WaterMarks').output.value", "type": "Expression"}, "isSequential": true, "activities": [{"name": "IfExcludedTable3", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@if(contains(pipeline().parameters.tables_to_exclude, item().table_name), false, true)", "type": "Expression"}, "ifTrueActivities": [{"name": "UpdateLastModifiedWatermarks", "type": "ExecuteDataFlow", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "UpdateLastModifiedWatermarks", "type": "DataFlowReference", "parameters": {"tableName": {"value": "'@{item().table_name}'", "type": "Expression"}, "db": "\"radixx\"", "waterMarkColumn": {"value": "'@{item().watermark_column_name}'", "type": "Expression"}}}, "compute": {"coreCount": 8, "computeType": "General"}, "traceLevel": "Fine"}}]}}]}}, {"name": "WatermarksDeltaToJson", "type": "ExecuteDataFlow", "dependsOn": [{"activity": "UpdateWatermarks", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "WatermarksDeltaToJson", "type": "DataFlowReference"}, "compute": {"coreCount": 8, "computeType": "General"}, "traceLevel": "Fine"}}], "parameters": {"db": {"type": "string", "defaultValue": "radixx"}, "tables_to_exclude": {"type": "array", "defaultValue": []}}, "variables": {"upsert_column_names": {"type": "Array", "defaultValue": [" "]}}, "folder": {"name": "Ingestion"}, "annotations": []}}