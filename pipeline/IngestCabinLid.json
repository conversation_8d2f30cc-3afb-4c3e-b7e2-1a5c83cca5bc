{"name": "IngestCabinLid", "properties": {"activities": [{"name": "IngestCabinLid", "type": "Copy", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [{"name": "Source", "value": "dbo.cabin_lid"}, {"name": "Destination", "value": "synapse/lakehouse/delta/bronze/radixx/cabin_lid"}], "typeProperties": {"source": {"type": "SqlServerSource", "partitionOption": "None"}, "sink": {"type": "ParquetSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "ParquetWriteSettings"}}, "enableStaging": false, "validateDataConsistency": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "RadixxCabinLid", "type": "DatasetReference"}], "outputs": [{"referenceName": "DeltaBronzeRadixxCabinLid", "type": "DatasetReference"}]}], "folder": {"name": "Ingestion/Example"}, "annotations": []}}