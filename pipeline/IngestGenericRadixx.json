{"name": "IngestGenericRadixx", "properties": {"activities": [{"name": "IngestRadixx", "type": "Copy", "dependsOn": [], "policy": {"timeout": "2.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [{"name": "Source", "value": "dbo.cabin_lid"}, {"name": "Destination", "value": "synapse/lakehouse/delta/bronze/radixx/cabin_lid"}], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": {"value": "@concat(\n  'SELECT * FROM dbo.', pipeline().parameters.targetTable,\n  ' WHERE ', pipeline().parameters.watermarkColumn,\n  ' > ''', pipeline().parameters.lastWatermark, ''''\n)\n", "type": "Expression"}, "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "ParquetSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "ParquetWriteSettings"}}, "enableStaging": false, "validateDataConsistency": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "RadixxTable", "type": "DatasetReference"}], "outputs": [{"referenceName": "DeltaBronzeRadixx", "type": "DatasetReference", "parameters": {"tablename": {"value": "@concat(pipeline().parameters.targetTable, '.parquet')", "type": "Expression"}}}]}], "parameters": {"targetTable": {"type": "string", "defaultValue": "cabin_lid"}, "watermarkColumn": {"type": "string", "defaultValue": "last_modified_date"}, "lastWatermark": {"type": "string", "defaultValue": "1900-01-01"}}, "folder": {"name": "Ingestion"}, "annotations": []}}