{"name": "CreateBookingRate", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "create"}, "database": {"type": "string", "defaultValue": "booking_rate"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/BookingRate"}, "annotations": []}}