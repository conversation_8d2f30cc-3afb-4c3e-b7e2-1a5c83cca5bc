{"$schema": "http://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "1.0.0.0", "parameters": {"workspaceName": {"type": "string", "metadata": "Workspace name", "defaultValue": "dev-san-syn-lakehouse-001"}, "dev-san-syn-lakehouse-001-WorkspaceDefaultSqlServer_connectionString": {"type": "secureString", "metadata": "Secure string for 'connectionString' of 'dev-san-syn-lakehouse-001-WorkspaceDefaultSqlServer'", "defaultValue": "Integrated Security=False;Encrypt=True;Connection Timeout=30;Data Source=tcp:dev-san-syn-lakehouse-001.sql.azuresynapse.net,1433;Initial Catalog=@{linkedService().DBName}"}, "AzureKeyVault_properties_typeProperties_baseUrl": {"type": "string", "defaultValue": "@{linkedService().baseURL}"}, "CipherwaveRadixx_properties_typeProperties_server": {"type": "string", "defaultValue": "**********"}, "CipherwaveRadixx_properties_typeProperties_database": {"type": "string", "defaultValue": "Radixx"}, "CipherwaveRadixx_properties_typeProperties_userName": {"type": "string", "defaultValue": "@{linkedService().userName}"}, "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage_properties_typeProperties_url": {"type": "string", "defaultValue": "https://devsanstlakehouse001.dfs.core.windows.net"}}, "variables": {"workspaceId": "[concat('Microsoft.Synapse/workspaces/', parameters('workspaceName'))]"}, "resources": [{"name": "[concat(parameters('workspaceName'), '/CreateBookingRate')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "create"}, "database": {"type": "string", "defaultValue": "booking_rate"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/BookingRate"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/CreateBronze')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "create"}, "database": {"type": "string", "defaultValue": "bronze"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/Bronze"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/CreateConfigs')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "create"}, "database": {"type": "string", "defaultValue": "configs"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/Configs"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/CreateGold')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "create"}, "database": {"type": "string", "defaultValue": "gold"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/Gold"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/CreateMLModelsOutput')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "create"}, "database": {"type": "string", "defaultValue": "ml_models_output"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/MLModelsOutput"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/CreateMetrics')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "create"}, "database": {"type": "string", "defaultValue": "metrics"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/Metrics"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/DropBookingRate')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "drop"}, "database": {"type": "string", "defaultValue": "booking_rate"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/BookingRate"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/DropBronze')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "drop"}, "database": {"type": "string", "defaultValue": "bronze"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/Bronze"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/DropConfigs')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "drop"}, "database": {"type": "string", "defaultValue": "configs"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/Configs"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/DropGold')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "drop"}, "database": {"type": "string", "defaultValue": "gold"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/Gold"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/DropMLModelsOutput')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "drop"}, "database": {"type": "string", "defaultValue": "ml_models_output"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/MLModelsOutput"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/DropMetrics')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "lake_database_action", "type": "SparkJob", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"sparkJob": {"referenceName": "lake_database_action", "type": "SparkJobDefinitionReference"}, "file": {"value": "@concat('abfss://synapse@', pipeline().parameters.storageAccount, '.dfs.core.windows.net/synapse/workspaces/',variables('env'),'-san-syn-lakehouse-001/batchjobs/bronze_lake_database_create/lake_db.py')", "type": "Expression"}, "args": ["--action", "@pipeline().parameters.action", "--database", "@pipeline().parameters.database"], "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null, "configurationType": "<PERSON><PERSON><PERSON>"}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"storageAccount": {"type": "string", "defaultValue": "devsanstlakehouse001"}, "action": {"type": "string", "defaultValue": "drop"}, "database": {"type": "string", "defaultValue": "metrics"}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "LakeDatabase/Metrics"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/sparkJobDefinitions/lake_database_action')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/FullRadixxPipeline')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "JobConfigs", "type": "Lookup", "dependsOn": [{"activity": "SetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "JsonSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFileName": "*.json", "enablePartitionDiscovery": false}, "formatSettings": {"type": "JsonReadSettings"}}, "dataset": {"referenceName": "databricksjobsconfigs", "type": "DatasetReference", "parameters": {}}, "firstRowOnly": false}}, {"name": "SetUnityCatalogClusterIDUC", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateUnityCatalog", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "clusterID", "value": {"value": "@activity('FilterUpdateUnityCatalog').output.value[0].cluster_id", "type": "Expression"}}}, {"name": "FilterUpdateUnityCatalog", "type": "Filter", "dependsOn": [{"activity": "JobConfigs", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('JobConfigs').output.value", "type": "Expression"}, "condition": {"value": "@and(equals(item().job_name, 'UpdateUnityCatalog'), equals(item().env, variables('env')) )", "type": "Expression"}}}, {"name": "IngestRadixx", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IngestRadixx", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {}}}, {"name": "ReservationsSQLQueryUpsert", "type": "SynapseNotebook", "dependsOn": [{"activity": "FilterUpdateMLReservations", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "ReservationsSQLQueryUpsert", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "UpdateUnityCatalog", "type": "DatabricksJob", "dependsOn": [{"activity": "IngestRadixx", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogClusterIDUC", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogDatabricksURLUC", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogJobIDUC", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"jobId": {"value": "@variables('jobID')", "type": "Expression"}}, "linkedServiceName": {"referenceName": "LakehouseAzureDatabricks", "type": "LinkedServiceReference", "parameters": {"keyvaultbaseURL": {"value": "@concat('https://', variables('env'), '-san-kv-lakehouse-001.vault.azure.net/')", "type": "Expression"}, "clusterID": {"value": "@variables('clusterID')", "type": "Expression"}, "databricksURL": {"value": "@variables('databricksURL')", "type": "Expression"}, "secretName": {"value": "@pipeline().parameters.databricksAccessTokenName", "type": "Expression"}}}}, {"name": "UpdateMetrics", "type": "DatabricksJob", "dependsOn": [{"activity": "SetUnityCatalogClusterIDMetrics", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogDatabricksURLMetrics", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogJobIDMetrics", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"jobId": {"value": "@variables('jobID')", "type": "Expression"}}, "linkedServiceName": {"referenceName": "LakehouseAzureDatabricks", "type": "LinkedServiceReference", "parameters": {"keyvaultbaseURL": {"value": "@concat('https://', variables('env'), '-san-kv-lakehouse-001.vault.azure.net/')", "type": "Expression"}, "clusterID": {"value": "@variables('clusterID')", "type": "Expression"}, "databricksURL": {"value": "@variables('databricksURL')", "type": "Expression"}, "secretName": {"value": "@pipeline().parameters.databricksAccessTokenName", "type": "Expression"}}}}, {"name": "UpdateMLReservations", "type": "DatabricksJob", "dependsOn": [{"activity": "ReservationsSQLQueryUpsert", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogClusterIDML", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogDatabricksURLML", "dependencyConditions": ["Succeeded"]}, {"activity": "SetUnityCatalogJobIDDatabricksURL", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"jobId": {"value": "@variables('jobID')", "type": "Expression"}}, "linkedServiceName": {"referenceName": "LakehouseAzureDatabricks", "type": "LinkedServiceReference", "parameters": {"keyvaultbaseURL": {"value": "@concat('https://', variables('env'), '-san-kv-lakehouse-001.vault.azure.net/')", "type": "Expression"}, "clusterID": {"value": "@variables('clusterID')", "type": "Expression"}, "databricksURL": {"value": "@variables('databricksURL')", "type": "Expression"}, "secretName": {"value": "@pipeline().parameters.databricksAccessTokenName", "type": "Expression"}}}}, {"name": "SetUnityCatalogDatabricksURLUC", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateUnityCatalog", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "databricksURL", "value": {"value": "@activity('FilterUpdateUnityCatalog').output.value[0].databricks_url", "type": "Expression"}}}, {"name": "FilterUpdateMetrics", "type": "Filter", "dependsOn": [{"activity": "JobConfigs", "dependencyConditions": ["Succeeded"]}, {"activity": "UpdateUnityCatalog", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('JobConfigs').output.value", "type": "Expression"}, "condition": {"value": "@and(equals(item().job_name, 'UpdateMetrics'), equals(item().env, variables('env')))", "type": "Expression"}}}, {"name": "SetUnityCatalogClusterIDMetrics", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMetrics", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "clusterID", "value": {"value": "@activity('FilterUpdateMetrics').output.value[0].cluster_id", "type": "Expression"}}}, {"name": "SetUnityCatalogDatabricksURLMetrics", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMetrics", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "databricksURL", "value": {"value": "@activity('FilterUpdateMetrics').output.value[0].databricks_url", "type": "Expression"}}}, {"name": "FilterUpdateMLReservations", "type": "Filter", "dependsOn": [{"activity": "JobConfigs", "dependencyConditions": ["Succeeded"]}, {"activity": "UpdateMetrics", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('JobConfigs').output.value", "type": "Expression"}, "condition": {"value": "@and(equals(item().job_name, 'UpdateMLReservations'), equals(item().env, variables('env')) )", "type": "Expression"}}}, {"name": "SetUnityCatalogClusterIDML", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMLReservations", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "clusterID", "value": {"value": "@activity('FilterUpdateMLReservations').output.value[0].cluster_id", "type": "Expression"}}}, {"name": "SetUnityCatalogDatabricksURLML", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMLReservations", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "databricksURL", "value": {"value": "@activity('FilterUpdateMLReservations').output.value[0].databricks_url", "type": "Expression"}}}, {"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}, {"name": "SetUnityCatalogJobIDUC", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateUnityCatalog", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "jobID", "value": {"value": "@activity('FilterUpdateUnityCatalog').output.value[0].job_id", "type": "Expression"}}}, {"name": "SetUnityCatalogJobIDMetrics", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMetrics", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "jobID", "value": {"value": "@activity('FilterUpdateMetrics').output.value[0].job_id", "type": "Expression"}}}, {"name": "SetUnityCatalogJobIDDatabricksURL", "type": "SetVariable", "dependsOn": [{"activity": "FilterUpdateMLReservations", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "jobID", "value": {"value": "@activity('FilterUpdateMLReservations').output.value[0].job_id", "type": "Expression"}}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"databricksAccessTokenName": {"type": "string", "defaultValue": "lakehouse-databricks-access-token"}}, "variables": {"databricksURL": {"type": "String", "defaultValue": "https://adb-293568896631603.3.azuredatabricks.net/"}, "clusterID": {"type": "String", "defaultValue": "0703-124105-oa6zhuqc"}, "env": {"type": "String", "defaultValue": "dev"}, "jobID": {"type": "Integer"}}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/datasets/databricksjobsconfigs')]", "[concat(variables('workspaceId'), '/pipelines/IngestRadixx')]", "[concat(variables('workspaceId'), '/notebooks/ReservationsSQLQueryUpsert')]", "[concat(variables('workspaceId'), '/linkedServices/LakehouseAzureDatabricks')]", "[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/GenEnv')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "GetEnv", "type": "SynapseNotebook", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"notebook": {"referenceName": "GetEnv", "type": "NotebookReference"}, "snapshot": true, "conf": {"spark.dynamicAllocation.enabled": null, "spark.dynamicAllocation.minExecutors": null, "spark.dynamicAllocation.maxExecutors": null}, "numExecutors": null}}, {"name": "SetEnv", "type": "SetVariable", "dependsOn": [{"activity": "GetEnv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "pipelineReturnValue", "value": [{"key": "env", "value": {"type": "Expression", "content": "@activity('GetEnv').output.status.Output.result.exitValue"}}], "setSystemVariable": true}}], "policy": {"elapsedTimeMetric": {}}, "variables": {"env": {"type": "String", "defaultValue": "dev"}}, "folder": {"name": "Helper<PERSON><PERSON><PERSON><PERSON>"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/notebooks/GetEnv')]"]}, {"name": "[concat(parameters('workspaceName'), '/IngestCabinLid')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "IngestCabinLid", "type": "Copy", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [{"name": "Source", "value": "dbo.cabin_lid"}, {"name": "Destination", "value": "synapse/lakehouse/delta/bronze/radixx/cabin_lid"}], "typeProperties": {"source": {"type": "SqlServerSource", "partitionOption": "None"}, "sink": {"type": "ParquetSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "ParquetWriteSettings"}}, "enableStaging": false, "validateDataConsistency": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "RadixxCabinLid", "type": "DatasetReference", "parameters": {}}], "outputs": [{"referenceName": "DeltaBronzeRadixxCabinLid", "type": "DatasetReference", "parameters": {}}]}], "policy": {"elapsedTimeMetric": {}}, "folder": {"name": "Ingestion/Example"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/datasets/RadixxCabinLid')]", "[concat(variables('workspaceId'), '/datasets/DeltaBronzeRadixxCabinLid')]"]}, {"name": "[concat(parameters('workspaceName'), '/IngestGenericRadixx')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "IngestRadixx", "type": "Copy", "dependsOn": [], "policy": {"timeout": "2.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [{"name": "Source", "value": "dbo.cabin_lid"}, {"name": "Destination", "value": "synapse/lakehouse/delta/bronze/radixx/cabin_lid"}], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": {"value": "@concat(\n  'SELECT * FROM dbo.', pipeline().parameters.targetTable,\n  ' WHERE ', pipeline().parameters.watermarkColumn,\n  ' > ''', pipeline().parameters.lastWatermark, ''''\n)\n", "type": "Expression"}, "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "ParquetSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "ParquetWriteSettings"}}, "enableStaging": false, "validateDataConsistency": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "RadixxTable", "type": "DatasetReference", "parameters": {}}], "outputs": [{"referenceName": "DeltaBronzeRadixx", "type": "DatasetReference", "parameters": {"tablename": {"value": "@concat(pipeline().parameters.targetTable, '.parquet')", "type": "Expression"}}}]}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"targetTable": {"type": "string", "defaultValue": "cabin_lid"}, "watermarkColumn": {"type": "string", "defaultValue": "last_modified_date"}, "lastWatermark": {"type": "string", "defaultValue": "1900-01-01"}}, "folder": {"name": "Ingestion"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/datasets/RadixxTable')]", "[concat(variables('workspaceId'), '/datasets/DeltaBronzeRadixx')]"]}, {"name": "[concat(parameters('workspaceName'), '/IngestRadixx')]", "type": "Microsoft.Synapse/workspaces/pipelines", "apiVersion": "2019-06-01-preview", "properties": {"activities": [{"name": "WaterMarks", "type": "Lookup", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "JsonSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFileName": "*.json", "enablePartitionDiscovery": false}, "formatSettings": {"type": "JsonReadSettings"}}, "dataset": {"referenceName": "metadajsonwatermarks", "type": "DatasetReference", "parameters": {}}, "firstRowOnly": false}}, {"name": "FetchRadixx", "type": "ForEach", "dependsOn": [{"activity": "WaterMarks", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('WaterMarks').output.value", "type": "Expression"}, "isSequential": false, "activities": [{"name": "IfExcludedTable1", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@if(contains(pipeline().parameters.tables_to_exclude, item().table_name), false, true)", "type": "Expression"}, "ifTrueActivities": [{"name": "IngestGenericRadixx", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IngestGenericRadixx", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"targetTable": {"value": "@item().table_name", "type": "Expression"}, "watermarkColumn": {"value": "@item().watermark_column_name", "type": "Expression"}, "lastWatermark": {"value": "@item().last_watermark", "type": "Expression"}}}}]}}]}}, {"name": "ParquetToDelta", "type": "ForEach", "dependsOn": [{"activity": "FetchRadixx", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('WaterMarks').output.value", "type": "Expression"}, "activities": [{"name": "IfExcludedTable2", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@if(contains(pipeline().parameters.tables_to_exclude, item().table_name), false, true)", "type": "Expression"}, "ifTrueActivities": [{"name": "GenericParquetToBronzeDelta", "type": "ExecuteDataFlow", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "GenericParquetToBronzeDelta", "type": "DataFlowReference", "parameters": {"source": {"value": "'@{item().table_name}'", "type": "Expression"}, "db": {"value": "'@{pipeline().parameters.db}'", "type": "Expression"}, "upsert_column_names": {"value": "@item().primary_key_columns", "type": "Expression"}}, "datasetParameters": {"ParquetSource": {}, "BronzeDeltaSink": {}}}, "staging": {}, "compute": {"coreCount": 16, "computeType": "General"}, "traceLevel": "Fine"}}]}}]}}, {"name": "UpdateWatermarks", "type": "ForEach", "dependsOn": [{"activity": "ParquetToDelta", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('WaterMarks').output.value", "type": "Expression"}, "isSequential": true, "activities": [{"name": "IfExcludedTable3", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@if(contains(pipeline().parameters.tables_to_exclude, item().table_name), false, true)", "type": "Expression"}, "ifTrueActivities": [{"name": "UpdateLastModifiedWatermarks", "type": "ExecuteDataFlow", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "UpdateLastModifiedWatermarks", "type": "DataFlowReference", "parameters": {"tableName": {"value": "'@{item().table_name}'", "type": "Expression"}, "db": "\"radixx\"", "waterMarkColumn": {"value": "'@{item().watermark_column_name}'", "type": "Expression"}}, "datasetParameters": {"BronzeDeltaSource": {}, "UpdateWatermark": {}}}, "staging": {}, "compute": {"coreCount": 8, "computeType": "General"}, "traceLevel": "Fine"}}]}}]}}, {"name": "WatermarksDeltaToJson", "type": "ExecuteDataFlow", "dependsOn": [{"activity": "UpdateWatermarks", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "WatermarksDeltaToJson", "type": "DataFlowReference", "parameters": {}, "datasetParameters": {"DeltaSource": {}, "JsonSink": {}}}, "staging": {}, "compute": {"coreCount": 8, "computeType": "General"}, "traceLevel": "Fine"}}], "policy": {"elapsedTimeMetric": {}}, "parameters": {"db": {"type": "string", "defaultValue": "radixx"}, "tables_to_exclude": {"type": "array", "defaultValue": []}}, "variables": {"upsert_column_names": {"type": "Array", "defaultValue": [" "]}}, "folder": {"name": "Ingestion"}, "annotations": []}, "dependsOn": ["[concat(variables('workspaceId'), '/datasets/metadajsonwatermarks')]", "[concat(variables('workspaceId'), '/dataflows/WatermarksDeltaToJson')]", "[concat(variables('workspaceId'), '/pipelines/IngestGenericRadixx')]", "[concat(variables('workspaceId'), '/dataflows/GenericParquetToBronzeDelta')]", "[concat(variables('workspaceId'), '/dataflows/UpdateLastModifiedWatermarks')]"]}, {"name": "[concat(parameters('workspaceName'), '/DeltaBronzeRadixx')]", "type": "Microsoft.Synapse/workspaces/datasets", "apiVersion": "2019-06-01-preview", "properties": {"linkedServiceName": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "parameters": {"tablename": {"type": "string", "defaultValue": "cabin_lid.parquet"}}, "folder": {"name": "Bronze"}, "annotations": [], "type": "Pa<PERSON><PERSON>", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "fileName": {"value": "@dataset().tablename", "type": "Expression"}, "folderPath": "lakehouse/parquet/landing-zone/radixx", "fileSystem": "synapse"}, "compressionCodec": "snappy"}, "schema": []}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/dev-san-syn-lakehouse-001-WorkspaceDefaultStorage')]"]}, {"name": "[concat(parameters('workspaceName'), '/DeltaBronzeRadixxCabinLid')]", "type": "Microsoft.Synapse/workspaces/datasets", "apiVersion": "2019-06-01-preview", "properties": {"linkedServiceName": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "folder": {"name": "Bronze"}, "annotations": [], "type": "Pa<PERSON><PERSON>", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "fileName": "cabin_lid", "folderPath": "lakehouse/delta/bronze/radixx", "fileSystem": "synapse"}, "compressionCodec": "snappy"}, "schema": []}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/dev-san-syn-lakehouse-001-WorkspaceDefaultStorage')]"]}, {"name": "[concat(parameters('workspaceName'), '/RadixxCabinLid')]", "type": "Microsoft.Synapse/workspaces/datasets", "apiVersion": "2019-06-01-preview", "properties": {"linkedServiceName": {"referenceName": "CipherwaveRadixx", "type": "LinkedServiceReference"}, "folder": {"name": "Radixx"}, "annotations": [], "type": "SqlServerTable", "schema": [{"name": "logical_flight_id", "type": "decimal", "precision": 38, "scale": 0}, {"name": "departure_date", "type": "datetime2", "scale": 7}, {"name": "cabin", "type": "<PERSON><PERSON><PERSON>"}, {"name": "cabin_capacity", "type": "decimal", "precision": 38, "scale": 0}, {"name": "cabin_lid", "type": "decimal", "precision": 38, "scale": 0}, {"name": "created_by", "type": "<PERSON><PERSON><PERSON>"}, {"name": "created_date", "type": "datetime2", "scale": 7}, {"name": "last_modified_by", "type": "<PERSON><PERSON><PERSON>"}, {"name": "last_modified_date", "type": "datetime2", "scale": 7}], "typeProperties": {"schema": "dbo", "table": "cabin_lid"}}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/CipherwaveRadixx')]"]}, {"name": "[concat(parameters('workspaceName'), '/RadixxTable')]", "type": "Microsoft.Synapse/workspaces/datasets", "apiVersion": "2019-06-01-preview", "properties": {"linkedServiceName": {"referenceName": "CipherwaveRadixx", "type": "LinkedServiceReference", "parameters": {"keyvaultBaseUrl": "https://dev-san-kv-lakehouse-001.vault.azure.net/"}}, "parameters": {"keyvaultBaseUrl": {"type": "string", "defaultValue": "https://dev-san-kv-lakehouse-001.vault.azure.net/"}}, "annotations": [], "type": "SqlServerTable", "schema": [], "typeProperties": {}}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/CipherwaveRadixx')]"]}, {"name": "[concat(parameters('workspaceName'), '/databricksjobsconfigs')]", "type": "Microsoft.Synapse/workspaces/datasets", "apiVersion": "2019-06-01-preview", "properties": {"linkedServiceName": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "annotations": [], "type": "Json", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "folderPath": "lakehouse/json/configs/databricks_jobs", "fileSystem": "synapse"}}, "schema": {"type": "object", "properties": {"env": {"type": "string"}, "job_name": {"type": "string"}, "cluster_id": {"type": "string"}, "job_id": {"type": "integer"}, "databricks_url": {"type": "string"}}}}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/dev-san-syn-lakehouse-001-WorkspaceDefaultStorage')]"]}, {"name": "[concat(parameters('workspaceName'), '/metadajsonwatermarks')]", "type": "Microsoft.Synapse/workspaces/datasets", "apiVersion": "2019-06-01-preview", "properties": {"linkedServiceName": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "annotations": [], "type": "Json", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "fileName": "watermarks.json", "folderPath": "lakehouse/json/metadata", "fileSystem": "synapse"}}, "schema": {"type": "object", "properties": {"table_name": {"type": "string"}, "primary_key_columns": {"type": "array", "items": {"type": "string"}}, "watermark_column_name": {"type": "string"}, "last_watermark": {"type": "string"}, "updated_at": {"type": "string"}}}}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/dev-san-syn-lakehouse-001-WorkspaceDefaultStorage')]"]}, {"name": "[concat(parameters('workspaceName'), '/metadatawatermarks')]", "type": "Microsoft.Synapse/workspaces/datasets", "apiVersion": "2019-06-01-preview", "properties": {"linkedServiceName": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "annotations": [], "type": "Pa<PERSON><PERSON>", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "fileName": "watermarks.parquet", "folderPath": "lakehouse/parquet/metadata", "fileSystem": "synapse"}, "compressionCodec": "snappy"}, "schema": [{"name": "table_name", "type": "UTF8"}, {"name": "last_watermark", "type": "INT96"}, {"name": "updated_at", "type": "INT96"}]}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/dev-san-syn-lakehouse-001-WorkspaceDefaultStorage')]"]}, {"name": "[concat(parameters('workspaceName'), '/AzureKeyVault')]", "type": "Microsoft.Synapse/workspaces/linkedServices", "apiVersion": "2019-06-01-preview", "properties": {"parameters": {"baseURL": {"type": "string", "defaultValue": "https://dev-san-kv-lakehouse-001.vault.azure.net/"}}, "annotations": [], "type": "AzureKey<PERSON>ault", "typeProperties": {"baseUrl": "[parameters('AzureKeyVault_properties_typeProperties_baseUrl')]"}}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/CipherwaveRadixx')]", "type": "Microsoft.Synapse/workspaces/linkedServices", "apiVersion": "2019-06-01-preview", "properties": {"parameters": {"keyvaultBaseUrl": {"type": "string", "defaultValue": "https://dev-san-kv-lakehouse-001.vault.azure.net/"}, "userName": {"type": "string", "defaultValue": "<PERSON><PERSON><PERSON>"}, "secretName": {"type": "string", "defaultValue": "cpieters-cipherwave"}, "shirName": {"type": "string", "defaultValue": "dev-cipherwave-shir"}}, "annotations": [], "type": "SqlServer", "typeProperties": {"server": "[parameters('CipherwaveRadixx_properties_typeProperties_server')]", "database": "[parameters('CipherwaveRadixx_properties_typeProperties_database')]", "encrypt": "mandatory", "trustServerCertificate": true, "authenticationType": "SQL", "userName": "[parameters('CipherwaveRadixx_properties_typeProperties_userName')]", "password": {"type": "AzureKeyVaultSecret", "store": {"referenceName": "AzureKey<PERSON>ault", "type": "LinkedServiceReference", "parameters": {"baseURL": {"value": "@linkedService().keyvaultBaseUrl", "type": "Expression"}}}, "secretName": {"value": "@linkedService().secretName", "type": "Expression"}}, "alwaysEncryptedSettings": {"alwaysEncryptedAkvAuthType": "ManagedIdentity"}}, "connectVia": {"referenceName": "dev-cipherwave-shir", "type": "IntegrationRuntimeReference"}}, "dependsOn": ["[concat(variables('workspaceId'), '/integrationRuntimes/dev-cipherwave-shir')]", "[concat(variables('workspaceId'), '/linkedServices/AzureKeyVault')]"]}, {"name": "[concat(parameters('workspaceName'), '/LakehouseAzureDatabricks')]", "type": "Microsoft.Synapse/workspaces/linkedServices", "apiVersion": "2019-06-01-preview", "properties": {"parameters": {"keyvaultbaseURL": {"type": "string", "defaultValue": "https://dev-san-kv-lakehouse-001.vault.azure.net/"}, "clusterID": {"type": "string", "defaultValue": "0703-124105-oa6zhuqc"}, "databricksURL": {"type": "string", "defaultValue": "https://adb-293568896631603.3.azuredatabricks.net"}, "secretName": {"type": "string", "defaultValue": "lakehouse-databricks-access-token"}}, "annotations": [], "type": "AzureDatabricks", "typeProperties": {"domain": "@linkedService().databricksURL", "accessToken": {"type": "AzureKeyVaultSecret", "store": {"referenceName": "AzureKey<PERSON>ault", "type": "LinkedServiceReference", "parameters": {"baseURL": {"value": "@linkedService().keyvaultbaseURL", "type": "Expression"}}}, "secretName": {"value": "@linkedService().secretName", "type": "Expression"}}, "existingClusterId": "@linkedService().clusterID"}, "connectVia": {"referenceName": "AutoResolveIntegrationRuntime", "type": "IntegrationRuntimeReference"}}, "dependsOn": ["[concat(variables('workspaceId'), '/integrationRuntimes/AutoResolveIntegrationRuntime')]", "[concat(variables('workspaceId'), '/linkedServices/AzureKeyVault')]"]}, {"name": "[concat(parameters('workspaceName'), '/dev-san-syn-lakehouse-001-WorkspaceDefaultSqlServer')]", "type": "Microsoft.Synapse/workspaces/linkedServices", "apiVersion": "2019-06-01-preview", "properties": {"parameters": {"DBName": {"type": "String"}}, "annotations": [], "type": "AzureSqlDW", "typeProperties": {"connectionString": "[parameters('dev-san-syn-lakehouse-001-WorkspaceDefaultSqlServer_connectionString')]"}, "connectVia": {"referenceName": "AutoResolveIntegrationRuntime", "type": "IntegrationRuntimeReference"}}, "dependsOn": ["[concat(variables('workspaceId'), '/integrationRuntimes/AutoResolveIntegrationRuntime')]"]}, {"name": "[concat(parameters('workspaceName'), '/dev-san-syn-lakehouse-001-WorkspaceDefaultStorage')]", "type": "Microsoft.Synapse/workspaces/linkedServices", "apiVersion": "2019-06-01-preview", "properties": {"annotations": [], "type": "AzureBlobFS", "typeProperties": {"url": "[parameters('dev-san-syn-lakehouse-001-WorkspaceDefaultStorage_properties_typeProperties_url')]"}, "connectVia": {"referenceName": "AutoResolveIntegrationRuntime", "type": "IntegrationRuntimeReference"}}, "dependsOn": ["[concat(variables('workspaceId'), '/integrationRuntimes/AutoResolveIntegrationRuntime')]"]}, {"name": "[concat(parameters('workspaceName'), '/DaillyTrigger')]", "type": "Microsoft.Synapse/workspaces/triggers", "apiVersion": "2019-06-01-preview", "properties": {"description": "A temporary daily trigger", "annotations": [], "runtimeState": "Started", "pipelines": [{"pipelineReference": {"referenceName": "FullRadixxPipeline", "type": "PipelineReference"}, "parameters": {}}], "type": "ScheduleTrigger", "typeProperties": {"recurrence": {"frequency": "Day", "interval": 1, "startTime": "2025-07-01T09:48:00", "timeZone": "South Africa Standard Time", "schedule": {"minutes": [15], "hours": [0]}}}}, "dependsOn": ["[concat(variables('workspaceId'), '/pipelines/FullRadixxPipeline')]"]}, {"name": "[concat(parameters('workspaceName'), '/AutoResolveIntegrationRuntime')]", "type": "Microsoft.Synapse/workspaces/integrationRuntimes", "apiVersion": "2019-06-01-preview", "properties": {"type": "Managed", "typeProperties": {"computeProperties": {"location": "AutoResolve", "dataFlowProperties": {"computeType": "General", "coreCount": 8, "timeToLive": 0}}}, "managedVirtualNetwork": {"type": "ManagedVirtualNetworkReference", "referenceName": "default"}}, "dependsOn": ["[concat(variables('workspaceId'), '/managedVirtualNetworks/default')]"]}, {"name": "[concat(parameters('workspaceName'), '/dev-cipherwave-shir')]", "type": "Microsoft.Synapse/workspaces/integrationRuntimes", "apiVersion": "2019-06-01-preview", "properties": {"type": "SelfHosted", "description": "DEV SHIR in the Cipherwave environment where Radixx is hosted", "typeProperties": {}}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/GenericParquetToBronzeDelta')]", "type": "Microsoft.Synapse/workspaces/dataflows", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "ParquetToDelta"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "ParquetSource"}], "sinks": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "BronzeDeltaSink"}], "transformations": [{"name": "UpsertCondition"}], "scriptLines": ["parameters{", "     source as string (\"cabin_lid\"),", "     db as string (\"radixx\"),", "     upsert_column_names as string[] ([\"logical_flight_id\"])", "}", "source(allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     format: 'parquet',", "     fileSystem: 'synapse',", "     folderPath: (concat('lakehouse/parquet/landing-zone/', $db)),", "     fileName: (concat($source, '.parquet')),", "     mode: 'read') ~> ParquetSource", "ParquetSource alterRow(upsertIf(true())) ~> UpsertCondition", "UpsertCondition sink(allowSchemaDrift: true,", "     validateSchema: false,", "     format: 'delta',", "     fileSystem: 'synapse',", "     folderPath: (concat('lakehouse/delta/bronze/',$db, '/', $source)),", "     mergeSchema: false,", "     autoCompact: false,", "     optimizedWrite: false,", "     vacuum: 0,", "     deletable: false,", "     insertable: true,", "     updateable: false,", "     upsertable: true,", "     keys:($upsert_column_names),", "     umask: 0022,", "     preCommands: [],", "     postCommands: [],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true) ~> BronzeDeltaSink"]}}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/dev-san-syn-lakehouse-001-WorkspaceDefaultStorage')]"]}, {"name": "[concat(parameters('workspaceName'), '/UpdateLastModifiedWatermarks')]", "type": "Microsoft.Synapse/workspaces/dataflows", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "Watermarks"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "BronzeDeltaSource"}], "sinks": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "UpdateWatermark"}], "transformations": [{"name": "GetLastModified"}, {"name": "UpdatedAt"}, {"name": "UpsertConditional"}], "scriptLines": ["parameters{", "     tableName as string (\"cabin_lid\"),", "     db as string (\"radixx\"),", "     waterMarkColumn as string (\"last_modified_date\")", "}", "source(allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     format: 'delta',", "     fileSystem: 'synapse',", "     folderPath: (concat('lakehouse/delta/bronze/',$db, '/', $tableName))) ~> BronzeDeltaSource", "UpdatedAt aggregate(groupBy(table_name,", "          updated_at),", "     last_watermark = max(toString(byName($waterMarkColumn)))) ~> GetLastModified", "BronzeDeltaSource derive(updated_at = currentTimestamp(),", "          table_name = $tableName) ~> UpdatedAt", "GetLastModified alterRow(upsertIf(not(isNull(last_watermark)))) ~> UpsertConditional", "UpsertConditional sink(allowSchemaDrift: true,", "     validateSchema: false,", "     format: 'delta',", "     fileSystem: 'synapse',", "     folderPath: 'lakehouse/delta/metadata/watermarks',", "     mergeSchema: false,", "     autoCompact: false,", "     optimizedWrite: false,", "     vacuum: 0,", "     deletable: false,", "     insertable: true,", "     updateable: false,", "     upsertable: true,", "     keys:['table_name'],", "     umask: 0022,", "     preCommands: [],", "     postCommands: [],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true) ~> UpdateWatermark"]}}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/dev-san-syn-lakehouse-001-WorkspaceDefaultStorage')]"]}, {"name": "[concat(parameters('workspaceName'), '/WatermarksDeltaToJson')]", "type": "Microsoft.Synapse/workspaces/dataflows", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "Watermarks"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "DeltaSource"}], "sinks": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "JsonSink"}], "transformations": [], "scriptLines": ["source(allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     format: 'delta',", "     fileSystem: 'synapse',", "     folderPath: 'lakehouse/delta/metadata/watermarks') ~> DeltaSource", "DeltaSource sink(allowSchemaDrift: true,", "     validateSchema: false,", "     format: 'json',", "     fileSystem: 'synapse',", "     folderPath: 'lakehouse/json/metadata',", "     truncate: true,", "     partitionFileNames:['watermarks.json'],", "     umask: 0022,", "     preCommands: [],", "     postCommands: [],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true,", "     partitionBy('hash', 1)) ~> JsonSink"]}}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/dev-san-syn-lakehouse-001-WorkspaceDefaultStorage')]"]}, {"name": "[concat(parameters('workspaceName'), '/WatermarksDeltaToParquet')]", "type": "Microsoft.Synapse/workspaces/dataflows", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "Watermarks"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "DeltaSource"}], "sinks": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "ParquetSink"}], "transformations": [], "scriptLines": ["source(allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     format: 'delta',", "     fileSystem: 'synapse',", "     folderPath: 'lakehouse/delta/metadata/watermarks') ~> DeltaSource", "DeltaSource sink(allowSchemaDrift: true,", "     validateSchema: false,", "     format: 'parquet',", "     fileSystem: 'synapse',", "     folderPath: 'lakehouse/parquet/metadata',", "     truncate: true,", "     partitionFileNames:['watermarks.parquet'],", "     umask: 0022,", "     preCommands: [],", "     postCommands: [],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true,", "     partitionBy('hash', 1)) ~> ParquetSink"]}}, "dependsOn": ["[concat(variables('workspaceId'), '/linkedServices/dev-san-syn-lakehouse-001-WorkspaceDefaultStorage')]"]}, {"name": "[concat(parameters('workspaceName'), '/WorkspaceSystemIdentity')]", "type": "Microsoft.Synapse/workspaces/credentials", "apiVersion": "2019-06-01-preview", "properties": {"type": "ManagedIdentity", "typeProperties": {}}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/default')]", "type": "Microsoft.Synapse/workspaces/managedVirtualNetworks", "apiVersion": "2019-06-01-preview", "properties": {}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/default/synapse-ws-custstgacct--dev-san-syn-lakehouse-001-devsanstlakehouse001')]", "type": "Microsoft.Synapse/workspaces/managedVirtualNetworks/managedPrivateEndpoints", "apiVersion": "2019-06-01-preview", "properties": {"privateLinkResourceId": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Storage/storageAccounts/devsanstlakehouse001", "groupId": "dfs"}, "dependsOn": ["[concat(variables('workspaceId'), '/managedVirtualNetworks/default')]"]}, {"name": "[concat(parameters('workspaceName'), '/default/synapse-ws-sql--dev-san-syn-lakehouse-001')]", "type": "Microsoft.Synapse/workspaces/managedVirtualNetworks/managedPrivateEndpoints", "apiVersion": "2019-06-01-preview", "properties": {"privateLinkResourceId": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001", "groupId": "sql", "fqdns": ["dev-san-syn-lakehouse-001.4af41524-0cbc-43f0-a3c5-0d78f828a2f0.sql.azuresynapse.net"]}, "dependsOn": ["[concat(variables('workspaceId'), '/managedVirtualNetworks/default')]"]}, {"name": "[concat(parameters('workspaceName'), '/default/synapse-ws-sqlOnDemand--dev-san-syn-lakehouse-001')]", "type": "Microsoft.Synapse/workspaces/managedVirtualNetworks/managedPrivateEndpoints", "apiVersion": "2019-06-01-preview", "properties": {"privateLinkResourceId": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001", "groupId": "sqlOn<PERSON><PERSON><PERSON>", "fqdns": ["dev-san-syn-lakehouse-001-ondemand.4af41524-0cbc-43f0-a3c5-0d78f828a2f0.sql.azuresynapse.net"]}, "dependsOn": ["[concat(variables('workspaceId'), '/managedVirtualNetworks/default')]"]}, {"name": "[concat(parameters('workspaceName'), '/lake_database_action')]", "type": "Microsoft.Synapse/workspaces/sparkJobDefinitions", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "LakeDatabase"}, "targetBigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "requiredSparkVersion": "3.4", "language": "python", "scanFolder": false, "jobProperties": {"name": "lake_database_action", "file": "abfss://<EMAIL>/synapse/workspaces/dev-san-syn-lakehouse-001/batchjobs/lake_database_action/lake_db.py", "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "1", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "527b8e90-2acd-40b9-8c06-9689a7870337", "spark.synapse.context.sjdname": "lake_database_action"}, "args": ["--action", "drop", "--database", "bronze"], "jars": [], "pyFiles": [""], "files": [], "driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2}}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/CreateDatabricksJobsConfig')]", "type": "Microsoft.Synapse/workspaces/notebooks", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "Configs"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "84c9d4c0-249a-4bf0-b341-9dccaae5153b"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "Synapse PySpark"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "source": ["import json\n", "\n", "# ── ENV CONFIG ─────────────────────────────────────────────────────────\n", "env            = spark.conf.get(\"spark.env\", \"dev\")\n", "delta_path     = \"/lakehouse/delta/configs/databricks_jobs\"\n", "\n", "if env == \"dev\":\n", "    cluster_id     = \"0703-124105-oa6zhuqc\"\n", "    databricks_url = \"https://adb-293568896631603.3.azuredatabricks.net\"\n", "elif env == \"prod\":\n", "    cluster_id     = \"0703-144730-damyif77\"\n", "    databricks_url = \"https://adb-2773699199994749.9.azuredatabricks.net\"\n", "else:\n", "    raise ValueError(f\"Unknown env: {env!r}\")\n", "\n", "# ── JOBS (only edit this!) ──────────────────────────────────────────────\n", "jobs_json = \"\"\"\n", "[\n", "  {\n", "    \"job_name\": \"UpdateUnityCatalog\",\n", "    \"job_id\": { \"dev\": 248226193417623, \"prod\": 111222333444555 }\n", "  },\n", "  {\n", "    \"job_name\": \"UpdateMetrics\",\n", "    \"job_id\": { \"dev\": 750917922960840, \"prod\": 666777888999000 }\n", "  },\n", "  {\n", "    \"job_name\": \"UpdateMLReservations\",\n", "    \"job_id\": { \"dev\": 760251170268939, \"prod\": 123123123123123 }\n", "  }\n", "]\n", "\"\"\"\n", "\n", "# ── WRITE FUNCTION ────────────────────────────────────\n", "def write_databricks_jobs(jobs, env, cluster_id, url, path):\n", "    rows = []\n", "    for j in jobs:\n", "        jid = j[\"job_id\"].get(env)\n", "        if jid is None:\n", "            raise KeyError(f\"Missing job_id for {j['job_name']} in env {env}\")\n", "        rows.append((env, j[\"job_name\"], cluster_id, jid, url))\n", "\n", "    cols = [\"env\",\"job_name\",\"cluster_id\",\"job_id\",\"databricks_url\"]\n", "    df   = spark.createDataFrame(rows, cols)\n", "\n", "    # write Delta\n", "    (df.write\n", "       .format(\"delta\")\n", "       .mode(\"overwrite\")\n", "       .option(\"overwriteSchema\", \"true\")\n", "       .save(path)\n", "    )\n", "    return df\n", "\n", "# ── RUN & CAPTURE ─────────────────────────────────────────────────────\n", "jobs = json.loads(jobs_json)\n", "df   = write_databricks_jobs(jobs, env, cluster_id, databricks_url, delta_path)\n", "\n", "# ── JSON OUTPUT ───────────────────────────────────────────────────────\n", "json_path = delta_path.replace(\"/delta/\", \"/json/\")\n", "(df.write\n", "   .mode(\"overwrite\")\n", "   .json(json_path)\n", ")"], "outputs": [], "execution_count": 1}]}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/GetEnv')]", "type": "Microsoft.Synapse/workspaces/notebooks", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "Utils"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "81f30349-81c1-4e12-94bb-888443dd98e6"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "python"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "source": ["from notebookutils import mssparkutils"], "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["env = spark.conf.get(\"spark.env\")"], "outputs": [], "execution_count": null}, {"cell_type": "code", "source": ["mssparkutils.notebook.exit(env)"], "outputs": [], "execution_count": null}]}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/Reservations')]", "type": "Microsoft.Synapse/workspaces/notebooks", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "Debugging"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "755ad184-4013-4017-9d2a-60e190fbace4"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "Synapse PySpark"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "source": ["# Cell 1 — load all bronze tables “pure”\n", "df_reservation_segs   = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/reservation_segs\")\n", "df_reservations       = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/reservations\")\n", "df_flight_segments    = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/flight_segments\")\n", "df_fl_marketed_flights= spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/fl_marketed_flights\")\n", "df_promotions         = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/promotions\")\n", "df_res_seg_status     = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/res_seg_status\")\n", "df_travel_agencies    = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/travel_agencies\")\n", "df_res_channels       = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/res_channels\")\n", "df_fare_class         = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/fare_class\")\n", "df_seat_assignments   = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/seat_assignments\")\n", "df_vwCabin            = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/cabin_lid\")\n", "df_contact_info       = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/contact_info\")\n", "df_person_org         = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/person_org\")\n", ""], "outputs": [], "execution_count": 47}, {"cell_type": "code", "source": ["df_contact_info = df_contact_info.filter(<PERSON><PERSON>col(\"contact_type\") == 6)"], "outputs": [], "execution_count": 50}, {"cell_type": "code", "source": ["import pyspark.sql.functions as F"], "outputs": [], "execution_count": 6}, {"cell_type": "code", "source": ["from pyspark.sql.window import Window\n", "\n", "window = Window.partitionBy(\"logical_flight_id\", \"departure_date\").orderBy(F.desc(\"cabin_lid\"))\n", "df_vwCabin = df_vwCabin.withColumn(\"rank\", F.row_number().over(window)).filter(F.col(\"rank\") == 1).drop(\"rank\")"], "outputs": [], "execution_count": 52}, {"cell_type": "code", "source": ["import pandas as pd\n", "\n", "# show all columns\n", "pd.set_option('display.max_columns', None)\n", "# expand notebook width so it doesn’t wrap\n", "pd.set_option('display.width', None)\n", ""], "outputs": [], "execution_count": 9}, {"cell_type": "code", "source": ["df = df_reservations.filter(<PERSON><PERSON>col(\"confirmation_num\") == 'QR9FF0')"], "outputs": [], "execution_count": 53}, {"cell_type": "code", "source": ["df = df_reservation_segs.join(df,\n", "                              on=\"confirmation_num\", how=\"inner\")"], "outputs": [], "execution_count": 54}, {"cell_type": "code", "source": ["df = df.join(df_flight_segments,\n", "             on=[\"confirmation_num\",\"record_num\"], how=\"inner\")"], "outputs": [], "execution_count": 55}, {"cell_type": "code", "source": ["df = df.join(df_fl_marketed_flights,\n", "             (df.LOGICAL_FLIGHT_ID == df_fl_marketed_flights.logical_flight_id) &\n", "             (df.DEPARTURE_DATE   == df_fl_marketed_flights.actual_depart_date_lt),\n", "             how=\"inner\")"], "outputs": [], "execution_count": 56}, {"cell_type": "code", "source": ["df = df.join(df_promotions,\n", "             on=\"promotion_id\", how=\"left\")"], "outputs": [], "execution_count": 57}, {"cell_type": "code", "source": ["df = df.join(df_res_seg_status,\n", "             on=\"res_seg_status\", how=\"left\")"], "outputs": [], "execution_count": 58}, {"cell_type": "code", "source": ["df = df.join(df_travel_agencies,\n", "             on=\"iata_num\", how=\"left\")"], "outputs": [], "execution_count": 59}, {"cell_type": "code", "source": ["df = df.join(df_res_channels,\n", "             on=\"res_channel_id\", how=\"left\")"], "outputs": [], "execution_count": 60}, {"cell_type": "code", "source": ["df = df.join(df_fare_class,\n", "             on=\"fare_class_code\", how=\"left\")"], "outputs": [], "execution_count": 61}, {"cell_type": "code", "source": ["df = df.join(df_seat_assignments,\n", "             on=[\"confirmation_num\",\"record_num\"], how=\"left\")"], "outputs": [], "execution_count": 62}, {"cell_type": "code", "source": ["df = df.join(df_vwCabin,\n", "             on=[\"logical_flight_id\",\"departure_date\"], how=\"left\")"], "outputs": [], "execution_count": 63}, {"cell_type": "code", "source": ["df = df.join(df_contact_info,\n", "             on=\"person_org_id\", how=\"left\")"], "outputs": [], "execution_count": 65}, {"cell_type": "code", "source": ["df = df.join(df_person_org,\n", "             on=\"person_org_id\", how=\"left\")\n", ""], "outputs": [], "execution_count": 67}, {"cell_type": "code", "metadata": {"collapsed": false}, "source": ["display(df)"], "outputs": [], "execution_count": 69}, {"cell_type": "code", "source": ["df.count()"], "outputs": [], "execution_count": 68}]}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/ReservationsSQLQuery')]", "type": "Microsoft.Synapse/workspaces/notebooks", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "Utils"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "5dd5cd48-af6f-434c-9f5f-3af44d752f13"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "Synapse PySpark"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "source": ["from pyspark.sql.functions import col\n", "from pyspark.sql.types import StringType, DateType, LongType, IntegerType, TimestampType, StructType, StructField\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.dataframe import DataFrame"], "outputs": [], "execution_count": 1}, {"cell_type": "code", "source": ["spark.conf.set(\"spark.sql.parquet.datetimeRebaseModeInWrite\", \"LEGACY\")\n", "spark.conf.set(\"spark.sql.parquet.int96RebaseModeInWrite\", \"LEGACY\")"], "outputs": [], "execution_count": 2}, {"cell_type": "code", "metadata": {"collapsed": false}, "source": ["def run_sql_on_delta_files(spark: SparkSession) -> DataFrame:\n", "    \"\"\"\n", "    Creates temporary views on top of Delta files using direct paths and runs a raw SQL query,\n", "    but restricts cabin_lid to the max per (logical_flight_id, departure_date).\n", "    \"\"\"\n", "    base_path = \"/lakehouse/delta/bronze/radixx\"\n", "    tables = [\n", "        \"reservation_segs\", \"reservations\", \"flight_segments\",\n", "        \"fl_marketed_flights\", \"promotions\", \"res_seg_status\",\n", "        \"travel_agencies\", \"res_channels\", \"fare_class\",\n", "        \"seat_assignments\", \"cabin_lid\", \"contact_info\", \"person_org\"\n", "    ]\n", "    for t in tables:\n", "        spark.read.format(\"delta\") \\\n", "             .load(f\"{base_path}/{t}\") \\\n", "             .createOrReplaceTempView(t)\n", "\n", "    sql_query = \"\"\"\n", "    WITH cabin_max AS (\n", "      SELECT\n", "        logical_flight_id,\n", "        departure_date,\n", "        MAX(last_modified_date) AS last_modified_date,\n", "        CASE\n", "          WHEN SUM(cabin_lid) = 0    THEN 0\n", "          WHEN SUM(cabin_lid) >= 180 THEN 189\n", "          ELSE 165\n", "        END AS cabin_lid\n", "      FROM cabin_lid\n", "      GROUP BY logical_flight_id, departure_date\n", "    )\n", "    SELECT\n", "      rs.confirmation_num,\n", "      CAST(rs.book_date   + INTERVAL 2 HOURS AS DATE)       AS book_date_mod,\n", "      date_format(rs.book_date + INTERVAL 2 HOURS, 'HH:mm') AS book_time_mod,\n", "      concat(\n", "        hour(rs.book_date + INTERVAL 2 HOURS),\n", "        ' - ',\n", "        hour(rs.book_date + INTERVAL 3 HOURS)\n", "      ) AS book_hourly_mod,\n", "      rs.booking_agent              AS user_segment,\n", "      rs.crs_code                   AS gds_code,\n", "      rs.person_org_id,\n", "      rs.iata_num,\n", "      CASE WHEN rs.cancel_date IS NULL THEN 1 ELSE 0 END    AS cancel_flag,\n", "      CAST(rs.cancel_date   + INTERVAL 2 HOURS AS DATE)     AS cancel_date,\n", "      concat(rs.confirmation_num, rs.record_num)            AS segkey,\n", "      CASE WHEN rs.from_record_num >= 1 THEN 1 ELSE 0 END   AS flt_change,\n", "      rs.ptc_id                     AS pax_type,\n", "      concat(\n", "        hour(res.book_date + INTERVAL 2 HOURS),\n", "        ' - ',\n", "        hour(res.book_date + INTERVAL 3 HOURS)\n", "      ) AS book_hourly,\n", "      CAST(res.book_date + INTERVAL 2 HOURS AS DATE)      AS book_date,\n", "      date_format(res.book_date + INTERVAL 2 HOURS, 'HH:mm') AS book_time,\n", "\n", "      concat(fs.logical_flight_id, cast(fs.departure_date AS DATE)) AS flt_key,\n", "      fs.fare_class_code                  AS fare_class,\n", "      substring(fs.saved_fb_code, 2, 3)   AS fare_type,\n", "      fs.saved_fb_code                    AS fare_basis_code,\n", "      concat(cast(fs.departure_date AS DATE), fs.operating_flight_num) AS fn_key,\n", "      fs.operating_flight_num             AS flight_no,\n", "      concat(\n", "        substring(mf.flight_num, 3, 5), '-', mf.depart_station, '-', mf.arrive_station, '-', cast(mf.passenger_std_lt AS DATE)\n", "      ) AS infare_key,\n", "      mf.logical_flight_id,\n", "      mf.flight_status,\n", "      concat(hour(mf.passenger_std_lt), ' - ', hour(mf.passenger_std_lt) + 1) AS departure_hourly,\n", "      CAST(mf.passenger_std_lt AS DATE)   AS departure_date,\n", "      date_format(mf.passenger_std_lt, 'HH:mm') AS departure_time,\n", "      CAST(mf.passenger_sta_lt AS DATE)    AS arrival_date,\n", "      date_format(mf.passenger_sta_lt, 'HH:mm')  AS arrival_time,\n", "      mf.depart_station                 AS from_airport,\n", "      mf.arrive_station                 AS to_airport,\n", "      concat(mf.depart_station, ' - ', mf.arrive_station) AS route,\n", "\n", "      rss.res_seg_status_desc AS segment_status,\n", "      p.promotion_code        AS promo_code,\n", "      CASE WHEN p.promotion_code IS NULL THEN 0 ELSE 1 END AS promo_flag,\n", "      ta.legal_name           AS iata_name,\n", "      rch.res_channel         AS channel_segment,\n", "      fc.default_nest         AS fare_nesting,\n", "\n", "      sa.row_num               AS row_no,\n", "      sa.seat_column           AS seat,\n", "      sa.boarding_pass_num     AS boarding_pass,\n", "      sa.boarding_sequence,\n", "      sa.checkin_agent_username AS checkin_agent,\n", "\n", "      cl.cabin_lid             AS cabin_lid,\n", "\n", "      ci.contact_field         AS ID_Number,\n", "      po.date_of_birth,\n", "      CAST(datediff(current_date(), po.date_of_birth) / 365.25 AS INT) AS Age\n", "\n", "    FROM reservation_segs rs\n", "      JOIN reservations res\n", "        ON rs.confirmation_num = res.confirmation_num\n", "      JOIN flight_segments fs\n", "        ON rs.confirmation_num = fs.confirmation_num\n", "       AND rs.record_num = fs.record_num\n", "      JOIN fl_marketed_flights mf\n", "        ON fs.logical_flight_id = mf.logical_flight_id\n", "       AND fs.departure_date = mf.actual_depart_date_lt\n", "      LEFT JOIN promotions p\n", "        ON rs.promotion_id = p.promotion_id\n", "      LEFT JOIN res_seg_status rss\n", "        ON rs.res_seg_status = rss.res_seg_status\n", "      LEFT JOIN travel_agencies ta\n", "        ON rs.iata_num = ta.iata_num\n", "      LEFT JOIN res_channels rch\n", "        ON rch.res_channel_id = rs.res_channel_id\n", "      LEFT JOIN fare_class fc\n", "        ON fc.fare_class_code = fs.fare_class_code\n", "      LEFT JOIN seat_assignments sa\n", "        ON rs.confirmation_num = sa.confirmation_num\n", "       AND rs.record_num = sa.record_num\n", "      LEFT JOIN cabin_max cl\n", "        ON fs.logical_flight_id = cl.logical_flight_id\n", "       AND fs.departure_date = cl.departure_date\n", "      LEFT JOIN (\n", "        SELECT person_org_id, contact_field\n", "        FROM (\n", "          SELECT\n", "            person_org_id,\n", "            contact_field,\n", "            ROW_NUMBER() OVER (\n", "              PARTITION BY person_org_id\n", "              ORDER BY last_modified_date DESC\n", "            ) AS rn\n", "          FROM contact_info\n", "          WHERE contact_type = '6'\n", "        ) t\n", "        WHERE rn = 1\n", "      ) ci\n", "        ON rs.person_org_id = ci.person_org_id\n", "      LEFT JOIN person_org po\n", "        ON po.person_org_id = ci.person_org_id\n", "    WHERE \n", "      mf.last_modified_date BETWEEN '2023-01-01' AND '2025-06-30'\n", "      AND mf.flight_status NOT IN ('NOOP', 'CANCELED')\n", "    \"\"\"\n", "\n", "    return spark.sql(sql_query)\n", "\n", "\n", "# Usage:\n", "final_df = run_sql_on_delta_files(spark)\n", ""], "outputs": [], "execution_count": 9}, {"cell_type": "code", "source": ["# Define the target schema including nullability\n", "target_schema = StructType([\n", "    StructField(\"confirmation_num\", StringType(), True),\n", "    StructField(\"book_date_mod\", DateType(), True),\n", "    StructField(\"book_time_mod\", StringType(), True),\n", "    StructField(\"book_hourly_mod\", StringType(), True),\n", "    StructField(\"user_segment\", StringType(), True),\n", "    StructField(\"gds_code\", StringType(), True),\n", "    StructField(\"person_org_id\", LongType(), True),\n", "    StructField(\"iata_num\", StringType(), True),\n", "    StructField(\"cancel_flag\", IntegerType(), True),\n", "    StructField(\"cancel_date\", DateType(), True),\n", "    StructField(\"segkey\", StringType(), True),\n", "    StructField(\"flt_change\", IntegerType(), True),\n", "    StructField(\"pax_type\", LongType(), True),\n", "    StructField(\"book_hourly\", StringType(), True),\n", "    StructField(\"book_date\", DateType(), True),\n", "    StructField(\"book_time\", StringType(), True),\n", "    StructField(\"flt_key\", StringType(), True),\n", "    StructField(\"fare_class\", LongType(), True),\n", "    StructField(\"fare_type\", StringType(), True),\n", "    StructField(\"fare_basis_code\", StringType(), True),\n", "    StructField(\"fn_key\", StringType(), True),\n", "    StructField(\"flight_no\", StringType(), True),\n", "    StructField(\"infare_key\", StringType(), True),\n", "    StructField(\"logical_flight_id\", LongType(), True),\n", "    StructField(\"flight_status\", StringType(), True),\n", "    StructField(\"departure_hourly\", StringType(), True),\n", "    StructField(\"departure_date\", DateType(), True),\n", "    StructField(\"departure_time\", StringType(), True),\n", "    StructField(\"arrival_date\", DateType(), True),\n", "    StructField(\"arrival_time\", StringType(), True),\n", "    StructField(\"from_airport\", StringType(), True),\n", "    StructField(\"to_airport\", StringType(), True),\n", "    StructField(\"route\", StringType(), True),\n", "    StructField(\"segment_status\", StringType(), True),\n", "    StructField(\"promo_code\", StringType(), True),\n", "    StructField(\"promo_flag\", IntegerType(), True),\n", "    StructField(\"iata_name\", StringType(), True),\n", "    StructField(\"channel_segment\", StringType(), True),\n", "    StructField(\"fare_nesting\", LongType(), True),\n", "    StructField(\"row_no\", StringType(), True),\n", "    StructField(\"seat\", StringType(), True),\n", "    StructField(\"boarding_pass\", LongType(), True),\n", "    StructField(\"boarding_sequence\", StringType(), True),\n", "    StructField(\"checkin_agent\", StringType(), True),\n", "    StructField(\"cabin_lid\", IntegerType(), True),\n", "    StructField(\"ID_Number\", StringType(), True),\n", "    StructField(\"date_of_birth\", TimestampType(), True),\n", "    <PERSON><PERSON>ct<PERSON>ield(\"Age\", IntegerType(), True)\n", "])\n", "\n", "\n", "casted_df = final_df.select([\n", "    col(field.name).cast(field.dataType).alias(field.name)\n", "    for field in target_schema.fields\n", "])\n", "\n", "final_df_conformed = spark.createDataFrame(casted_df.rdd, schema=target_schema)"], "outputs": [], "execution_count": 10}, {"cell_type": "code", "source": ["final_df_conformed.write.format(\"delta\").mode(\"overwrite\").save(\"/lakehouse/delta/gold/ml_reservations\")"], "outputs": [], "execution_count": 12}]}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/ReservationsSQLQueryUpsert')]", "type": "Microsoft.Synapse/workspaces/notebooks", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "Utils"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "18f9fa9e-2434-48e8-b9ec-8afc088cca15"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "Synapse PySpark"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "source": ["from datetime import datetime, timedelta\n", "\n", "from pyspark.sql.functions import col\n", "from pyspark.sql.types import StringType, DateType, LongType, IntegerType, TimestampType, StructType, StructField\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.dataframe import DataFrame\n", "\n", "from delta.tables import DeltaTable"], "outputs": [], "execution_count": 2}, {"cell_type": "code", "source": ["spark.conf.set(\"spark.sql.parquet.datetimeRebaseModeInWrite\", \"LEGACY\")\n", "spark.conf.set(\"spark.sql.parquet.int96RebaseModeInWrite\", \"LEGACY\")"], "outputs": [], "execution_count": 3}, {"cell_type": "code", "source": ["yesterday = (datetime.now() - <PERSON><PERSON><PERSON>(days=1)).strftime('%Y-%m-%d')"], "outputs": [], "execution_count": 4}, {"cell_type": "code", "metadata": {"collapsed": false}, "source": ["def run_sql_on_delta_files(spark: SparkSession) -> DataFrame:\n", "    \"\"\"\n", "    Creates temporary views on top of Delta files using direct paths and runs a raw SQL query,\n", "    but restricts cabin_lid to the max per (logical_flight_id, departure_date).\n", "    \"\"\"\n", "    # Define the base path to your Delta tables using the direct file system path\n", "    base_path = \"/lakehouse/delta/bronze/radixx\"\n", "\n", "    # --- 1. Create Temporary Views for each table ---\n", "    table_names = [\n", "        \"reservation_segs\", \"reservations\", \"flight_segments\",\n", "        \"fl_marketed_flights\", \"promotions\", \"res_seg_status\",\n", "        \"travel_agencies\", \"res_channels\", \"fare_class\",\n", "        \"seat_assignments\", \"cabin_lid\", \"contact_info\", \"person_org\"\n", "    ]\n", "\n", "    for table in table_names:\n", "        spark.read.format(\"delta\") \\\n", "            .load(f\"{base_path}/{table}\") \\\n", "            .createOrReplaceTempView(table)\n", "\n", "    print(\"Temporary views created successfully.\")\n", "\n", "    # --- 2. Define your SQL query with a CTE for cabin_max ---\n", "    sql_query = f\"\"\"\n", "    WITH cabin_max AS (\n", "      SELECT\n", "        logical_flight_id,\n", "        departure_date,\n", "        MAX(last_modified_date) AS last_modified_date,\n", "        CASE\n", "          WHEN SUM(cabin_lid) = 0    THEN 0\n", "          WHEN SUM(cabin_lid) >= 180 THEN 189\n", "          ELSE 165\n", "        END AS cabin_lid\n", "      FROM cabin_lid\n", "      GROUP BY logical_flight_id, departure_date\n", "    )\n", "    SELECT\n", "      rs.confirmation_num,\n", "      CAST(rs.book_date   + INTERVAL 2 HOURS AS DATE)       AS book_date_mod,\n", "      date_format(rs.book_date + INTERVAL 2 HOURS, 'HH:mm') AS book_time_mod,\n", "      concat(\n", "        hour(rs.book_date + INTERVAL 2 HOURS),\n", "        ' - ',\n", "        hour(rs.book_date + INTERVAL 3 HOURS)\n", "      ) AS book_hourly_mod,\n", "      rs.booking_agent              AS user_segment,\n", "      rs.crs_code                   AS gds_code,\n", "      rs.person_org_id,\n", "      rs.iata_num,\n", "      CASE WHEN rs.cancel_date IS NULL THEN 1 ELSE 0 END    AS cancel_flag,\n", "      CAST(rs.cancel_date   + INTERVAL 2 HOURS AS DATE)     AS cancel_date,\n", "      concat(rs.confirmation_num, rs.record_num)            AS segkey,\n", "      CASE WHEN rs.from_record_num >= 1 THEN 1 ELSE 0 END   AS flt_change,\n", "      rs.ptc_id                     AS pax_type,\n", "      concat(\n", "        hour(res.book_date + INTERVAL 2 HOURS),\n", "        ' - ',\n", "        hour(res.book_date + INTERVAL 3 HOURS)\n", "      ) AS book_hourly,\n", "      CAST(res.book_date + INTERVAL 2 HOURS AS DATE)      AS book_date,\n", "      date_format(res.book_date + INTERVAL 2 HOURS, 'HH:mm') AS book_time,\n", "\n", "      concat(fs.logical_flight_id, cast(fs.departure_date AS DATE)) AS flt_key,\n", "      fs.fare_class_code                  AS fare_class,\n", "      substring(fs.saved_fb_code, 2, 3)   AS fare_type,\n", "      fs.saved_fb_code                    AS fare_basis_code,\n", "      concat(cast(fs.departure_date AS DATE), fs.operating_flight_num) AS fn_key,\n", "      fs.operating_flight_num             AS flight_no,\n", "      concat(\n", "        substring(mf.flight_num, 3, 5), '-', mf.depart_station, '-', mf.arrive_station, '-', cast(mf.passenger_std_lt AS DATE)\n", "      ) AS infare_key,\n", "      mf.logical_flight_id,\n", "      mf.flight_status,\n", "      concat(hour(mf.passenger_std_lt), ' - ', hour(mf.passenger_std_lt) + 1) AS departure_hourly,\n", "      CAST(mf.passenger_std_lt AS DATE)   AS departure_date,\n", "      date_format(mf.passenger_std_lt, 'HH:mm') AS departure_time,\n", "      CAST(mf.passenger_sta_lt AS DATE)    AS arrival_date,\n", "      date_format(mf.passenger_sta_lt, 'HH:mm')  AS arrival_time,\n", "      mf.depart_station                 AS from_airport,\n", "      mf.arrive_station                 AS to_airport,\n", "      concat(mf.depart_station, ' - ', mf.arrive_station) AS route,\n", "\n", "      rss.res_seg_status_desc AS segment_status,\n", "      p.promotion_code        AS promo_code,\n", "      CASE WHEN p.promotion_code IS NULL THEN 0 ELSE 1 END AS promo_flag,\n", "      ta.legal_name           AS iata_name,\n", "      rch.res_channel         AS channel_segment,\n", "      fc.default_nest         AS fare_nesting,\n", "\n", "      sa.row_num               AS row_no,\n", "      sa.seat_column           AS seat,\n", "      sa.boarding_pass_num     AS boarding_pass,\n", "      sa.boarding_sequence,\n", "      sa.checkin_agent_username AS checkin_agent,\n", "\n", "      cl.cabin_lid             AS cabin_lid,\n", "\n", "      ci.contact_field         AS ID_Number,\n", "      po.date_of_birth,\n", "      CAST(datediff(current_date(), po.date_of_birth) / 365.25 AS INT) AS Age\n", "\n", "    FROM reservation_segs rs\n", "      JOIN reservations res\n", "        ON rs.confirmation_num = res.confirmation_num\n", "      JOIN flight_segments fs\n", "        ON rs.confirmation_num = fs.confirmation_num\n", "       AND rs.record_num = fs.record_num\n", "      JOIN fl_marketed_flights mf\n", "        ON fs.logical_flight_id = mf.logical_flight_id\n", "       AND fs.departure_date = mf.actual_depart_date_lt\n", "      LEFT JOIN promotions p\n", "        ON rs.promotion_id = p.promotion_id\n", "      LEFT JOIN res_seg_status rss\n", "        ON rs.res_seg_status = rss.res_seg_status\n", "      LEFT JOIN travel_agencies ta\n", "        ON rs.iata_num = ta.iata_num\n", "      LEFT JOIN res_channels rch\n", "        ON rch.res_channel_id = rs.res_channel_id\n", "      LEFT JOIN fare_class fc\n", "        ON fc.fare_class_code = fs.fare_class_code\n", "      LEFT JOIN seat_assignments sa\n", "        ON rs.confirmation_num = sa.confirmation_num\n", "       AND rs.record_num = sa.record_num\n", "      LEFT JOIN cabin_max cl\n", "        ON fs.logical_flight_id = cl.logical_flight_id\n", "       AND fs.departure_date = cl.departure_date\n", "      LEFT JOIN (\n", "        SELECT person_org_id, contact_field\n", "        FROM (\n", "          SELECT\n", "            person_org_id,\n", "            contact_field,\n", "            ROW_NUMBER() OVER (\n", "              PARTITION BY person_org_id\n", "              ORDER BY last_modified_date DESC\n", "            ) AS rn\n", "          FROM contact_info\n", "          WHERE contact_type = '6'\n", "        ) t\n", "        WHERE rn = 1\n", "      ) ci\n", "        ON rs.person_org_id = ci.person_org_id\n", "      LEFT JOIN person_org po\n", "        ON po.person_org_id = ci.person_org_id\n", "    WHERE \n", "      mf.last_modified_date >= '{yesterday}'\n", "      AND mf.flight_status NOT IN ('NOOP', 'CANCELED')\n", "    \"\"\"\n", "\n", "    # --- 3. Execute the SQL query and return the result as a DataFrame ---\n", "    final_dataframe = spark.sql(sql_query)\n", "    return final_dataframe\n", "\n", "# Usage:\n", "final_df = run_sql_on_delta_files(spark)"], "outputs": [], "execution_count": 8}, {"cell_type": "code", "source": ["# Define the target schema including nullability\n", "target_schema = StructType([\n", "    StructField(\"confirmation_num\", StringType(), True),\n", "    StructField(\"book_date_mod\", DateType(), True),\n", "    StructField(\"book_time_mod\", StringType(), True),\n", "    StructField(\"book_hourly_mod\", StringType(), True),\n", "    StructField(\"user_segment\", StringType(), True),\n", "    StructField(\"gds_code\", StringType(), True),\n", "    StructField(\"person_org_id\", LongType(), True),\n", "    StructField(\"iata_num\", StringType(), True),\n", "    StructField(\"cancel_flag\", IntegerType(), True),\n", "    StructField(\"cancel_date\", DateType(), True),\n", "    StructField(\"segkey\", StringType(), True),\n", "    StructField(\"flt_change\", IntegerType(), True),\n", "    StructField(\"pax_type\", LongType(), True),\n", "    StructField(\"book_hourly\", StringType(), True),\n", "    StructField(\"book_date\", DateType(), True),\n", "    StructField(\"book_time\", StringType(), True),\n", "    StructField(\"flt_key\", StringType(), True),\n", "    StructField(\"fare_class\", LongType(), True),\n", "    StructField(\"fare_type\", StringType(), True),\n", "    StructField(\"fare_basis_code\", StringType(), True),\n", "    StructField(\"fn_key\", StringType(), True),\n", "    StructField(\"flight_no\", StringType(), True),\n", "    StructField(\"infare_key\", StringType(), True),\n", "    StructField(\"logical_flight_id\", LongType(), True),\n", "    StructField(\"flight_status\", StringType(), True),\n", "    StructField(\"departure_hourly\", StringType(), True),\n", "    StructField(\"departure_date\", DateType(), True),\n", "    StructField(\"departure_time\", StringType(), True),\n", "    StructField(\"arrival_date\", DateType(), True),\n", "    StructField(\"arrival_time\", StringType(), True),\n", "    StructField(\"from_airport\", StringType(), True),\n", "    StructField(\"to_airport\", StringType(), True),\n", "    StructField(\"route\", StringType(), True),\n", "    StructField(\"segment_status\", StringType(), True),\n", "    StructField(\"promo_code\", StringType(), True),\n", "    StructField(\"promo_flag\", IntegerType(), True),\n", "    StructField(\"iata_name\", StringType(), True),\n", "    StructField(\"channel_segment\", StringType(), True),\n", "    StructField(\"fare_nesting\", LongType(), True),\n", "    StructField(\"row_no\", StringType(), True),\n", "    StructField(\"seat\", StringType(), True),\n", "    StructField(\"boarding_pass\", LongType(), True),\n", "    StructField(\"boarding_sequence\", StringType(), True),\n", "    StructField(\"checkin_agent\", StringType(), True),\n", "    StructField(\"cabin_lid\", IntegerType(), True),\n", "    StructField(\"ID_Number\", StringType(), True),\n", "    StructField(\"date_of_birth\", TimestampType(), True),\n", "    <PERSON><PERSON>ct<PERSON>ield(\"Age\", IntegerType(), True)\n", "])\n", "\n", "\n", "casted_df = final_df.select([\n", "    col(field.name).cast(field.dataType).alias(field.name)\n", "    for field in target_schema.fields\n", "])\n", "\n", "final_df_conformed = spark.createDataFrame(casted_df.rdd, schema=target_schema)"], "outputs": [], "execution_count": 18}, {"cell_type": "code", "source": ["existing_table = DeltaTable.forPath(spark, \"/lakehouse/delta/gold/ml_reservations\")"], "outputs": [], "execution_count": 19}, {"cell_type": "code", "source": ["existing_table.alias(\"old\").merge(final_df_conformed.alias(\"new\"), \"old.segkey = new.segkey\").whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()"], "outputs": [], "execution_count": 20}]}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/init_watermarks')]", "type": "Microsoft.Synapse/workspaces/notebooks", "apiVersion": "2019-06-01-preview", "properties": {"folder": {"name": "<PERSON><PERSON><PERSON>"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "db32ca0a-beae-4f27-aebb-cec8e2efc494"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "Synapse PySpark"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "metadata": {"microsoft": {"language": "sparksql"}, "collapsed": false}, "source": ["%%sql\n", "CREATE DATABASE IF NOT EXISTS metadata"], "outputs": [], "execution_count": 2}, {"cell_type": "code", "source": ["# Synapse / Databricks Notebook: Seed watermark Delta with PK + Watermark Column Info\n", "# ------------------------------------------------------------------------------------\n", "# 1) Imports & parameters\n", "from pyspark.sql.types import StructType, StructField, StringType, TimestampType, ArrayType\n", "from delta.tables       import DeltaTable\n", "import datetime\n", "\n", "metadata_path = \"/lakehouse/delta/metadata/watermarks\"\n", "baseline_dt   = datetime.datetime(1900, 1, 1)\n", "\n", "# 2) Define the Delta schema (including PK columns list and watermark column name)\n", "schema = StructType([\n", "    StructField(\"table_name\",                StringType(),  False),\n", "    StructField(\"primary_key_columns\",       ArrayType(StringType()), False),\n", "    StructField(\"watermark_column_name\",     StringType(),  False),\n", "    StructField(\"last_watermark\",            TimestampType(), False),\n", "    StructField(\"updated_at\",                TimestampType(), False),\n", "])\n", "\n", "# 3) Create the Delta if it doesn’t exist\n", "if not DeltaTable.isDeltaTable(spark, metadata_path):\n", "    spark.createDataFrame([], schema) \\\n", "         .write.format(\"delta\") \\\n", "         .mode(\"overwrite\") \\\n", "         .save(metadata_path)\n", "\n", "# 4) Read existing metadata\n", "df_meta = spark.read.format(\"delta\").load(metadata_path)\n", "\n", "# 5) Seed initial rows if table is empty\n", "if df_meta.rdd.isEmpty():\n", "    initial_watermarks = [\n", "        # table_name              primary_key_columns                                          watermark_column_name    last_wm        updated_at\n", "        (\"reservation_segs\",     [\"series_num\", \"confirmation_num\", \"record_num\"],             \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"flight_segments\",      [\"SERIES_NUM\", \"CONFIRMATION_NUM\", \"RECORD_NUM\"],             \"LAST_MODIFIED_DATE\",    baseline_dt,   baseline_dt),\n", "        (\"reservations\",         [\"series_num\", \"confirmation_num\"],                           \"last_modified\",         baseline_dt,   baseline_dt),\n", "        (\"fl_marketed_flights\",  [\"logical_flight_id\"],                                        \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"promotions\",           [\"promotion_id\"],                                             \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"res_seg_status\",       [\"res_seg_status\"],                                           \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"res_channels\",         [\"res_channel_id\"],                                           \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"seat_assignments\",     [\"series_num\", \"confirmation_num\", \"record_num\", \"leg_order\"],\"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"res_charges\",          [\"res_charge_id\"],                                            \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"cabin_lid\",            [\"logical_flight_id\", \"departure_date\", \"cabin\"],             \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"res_payment_map\",      [\"res_payment_id\", \"res_charge_id\"],                          \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"travel_agencies\"),     [\"iata_num\"],                                                 \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"fare_class\"),          [\"fare_class_code\"],                                          \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"contact_info\"),        [\"contact_id\"],                                               \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"person_org\"),          [\"person_org_id\"],                                            \"last_modified_date\",    baseline_dt,   baseline_dt), \n", "    ]\n", "    spark.createDataFrame(initial_watermarks, schema) \\\n", "         .write.format(\"delta\") \\\n", "         .mode(\"overwrite\") \\\n", "         .save(metadata_path)"], "outputs": [], "execution_count": 2}, {"cell_type": "code", "metadata": {"microsoft": {"language": "sparksql"}, "collapsed": false}, "source": ["%%sql\n", "CREATE TABLE IF NOT EXISTS metadata.watermarks\n", "USING DELTA \n", "LOCATION \"/lakehouse/delta/metadata/watermarks\""], "outputs": [], "execution_count": 3}]}, "dependsOn": []}, {"name": "[concat(parameters('workspaceName'), '/lakeshousespark')]", "type": "Microsoft.Synapse/workspaces/bigDataPools", "apiVersion": "2019-06-01-preview", "properties": {"autoPause": {"enabled": true, "delayInMinutes": 15}, "autoScale": {"enabled": true, "maxNodeCount": 3, "minNodeCount": 3}, "nodeCount": 10, "nodeSize": "Small", "nodeSizeFamily": "MemoryOptimized", "sparkVersion": "3.4", "isComputeIsolationEnabled": false, "sparkConfigProperties": {"configurationType": "Artifact", "filename": "config_spark_pool_dev", "content": "{\"name\":\"config_spark_pool_dev\",\"properties\":{\"configs\":{\"spark.env\":\"dev\",\"spark.storage.file_system\":\"synapse\",\"spark.storage.storage_account\":\"devsanstlakehouse001\"},\"annotations\":[],\"type\":\"Microsoft.Synapse/workspaces/sparkconfigurations\",\"description\":\"\",\"notes\":\"\",\"created\":\"2025-07-02T10:48:00.3550000+02:00\",\"createdBy\":\"<EMAIL>\",\"configMergeRule\":{\"admin.currentOperation.spark.env\":\"replace\",\"admin.currentOperation.spark.storage.file_system\":\"replace\",\"admin.currentOperation.spark.storage.storage_account\":\"replace\"}}}", "time": "2025-07-02T08:49:10.2522731Z"}, "sessionLevelPackagesEnabled": true, "annotations": []}, "dependsOn": [], "location": "southafricanorth"}]}