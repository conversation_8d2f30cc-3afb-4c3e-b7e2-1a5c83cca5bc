{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"workspaceName": {"value": "dev-san-syn-lakehouse-001"}, "dev-san-syn-lakehouse-001-WorkspaceDefaultSqlServer_connectionString": {"value": "Integrated Security=False;Encrypt=True;Connection Timeout=30;Data Source=tcp:dev-san-syn-lakehouse-001.sql.azuresynapse.net,1433;Initial Catalog=@{linkedService().DBName}"}, "AzureKeyVault_properties_typeProperties_baseUrl": {"value": "@{linkedService().baseURL}"}, "CipherwaveRadixx_properties_typeProperties_server": {"value": "**********"}, "CipherwaveRadixx_properties_typeProperties_database": {"value": "Radixx"}, "CipherwaveRadixx_properties_typeProperties_userName": {"value": "@{linkedService().userName}"}, "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage_properties_typeProperties_url": {"value": "https://devsanstlakehouse001.dfs.core.windows.net"}}}