resources:
  jobs:
    create-ml-reservations:
      name: create-ml-reservations
      tasks:
        - task_key: create_ml_reservations_table
          spark_python_task:
            python_file: ${workspace.file_path}/src/spark/databricks/unity_catalog/create_tables.py
            parameters:
              - --env ${bundle.target}
              - --database gold
              - --tables ml_reservations
          existing_cluster_id: 0703-124105-oa6zhuqc
      queue:
        enabled: true
