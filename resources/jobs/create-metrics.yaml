resources:
  jobs:
    create-metrics:
      name: create-metrics
      tasks:
        - task_key: create_bronze_reservations_metrics
          spark_python_task:
            python_file: ${workspace.file_path}/src/spark/databricks/metrics/bronze_reservation_quality.py
            parameters:
              - --env ${bundle.target}
          existing_cluster_id: 0703-124105-oa6zhuqc
      queue:
        enabled: true
