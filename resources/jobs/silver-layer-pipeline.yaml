resources:
  jobs:
    silver-layer-pipeline:
      name: silver-layer-pipeline
      tasks:
        - task_key: silver_dim_agent
          spark_python_task:
            python_file: ${workspace.file_path}/src/spark/databricks/silver/dims/dim_agent.py
            parameters:
              - --env ${bundle.target}
          existing_cluster_id: 0703-124105-oa6zhuqc
        - task_key: silver_dim_currency
          depends_on:
            - task_key: silver_dim_agent
          spark_python_task:
            python_file: ${workspace.file_path}/src/spark/databricks/silver/dims/dim_currency.py
            parameters:
              - --env ${bundle.target}
          existing_cluster_id: 0703-124105-oa6zhuqc
      queue:
        enabled: true