resources:
  jobs:
    create-uc-tables:
      name: create-uc-tables
      tasks:
        - task_key: bronze_radixx_table_create
          spark_python_task:
            python_file: ${workspace.file_path}/src/spark/databricks/unity_catalog/create_tables.py
            parameters:
              - --env ${bundle.target}
              - --database bronze
              - --table-prefix radixx
              - --tables
                res_charges,promotions,reservation_segs,flight_segments,reservations,fl_marketed_flights,res_seg_status,res_channels,seat_assignments,cabin_lid,res_payment_map,fare_class,contact_info,travel_agencies,person_org
          existing_cluster_id: 0611-092118-o9bq8etz
      queue:
        enabled: true