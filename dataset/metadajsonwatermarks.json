{"name": "metadajsonwatermarks", "properties": {"linkedServiceName": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "annotations": [], "type": "Json", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "fileName": "watermarks.json", "folderPath": "lakehouse/json/metadata", "fileSystem": "synapse"}}, "schema": {"type": "object", "properties": {"table_name": {"type": "string"}, "primary_key_columns": {"type": "array", "items": {"type": "string"}}, "watermark_column_name": {"type": "string"}, "last_watermark": {"type": "string"}, "updated_at": {"type": "string"}}}}}