{"name": "DeltaBronzeRadixxCabinLid", "properties": {"linkedServiceName": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "folder": {"name": "Bronze"}, "annotations": [], "type": "Pa<PERSON><PERSON>", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "fileName": "cabin_lid", "folderPath": "lakehouse/delta/bronze/radixx", "fileSystem": "synapse"}, "compressionCodec": "snappy"}, "schema": []}}