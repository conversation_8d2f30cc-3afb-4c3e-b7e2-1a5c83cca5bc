{"name": "databricksjobsconfigs", "properties": {"linkedServiceName": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "annotations": [], "type": "Json", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "folderPath": "lakehouse/json/configs/databricks_jobs", "fileSystem": "synapse"}}, "schema": {"type": "object", "properties": {"env": {"type": "string"}, "job_name": {"type": "string"}, "cluster_id": {"type": "string"}, "job_id": {"type": "integer"}, "databricks_url": {"type": "string"}}}}}