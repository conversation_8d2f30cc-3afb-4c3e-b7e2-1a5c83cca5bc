{"name": "DeltaBronzeRadixx", "properties": {"linkedServiceName": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "parameters": {"tablename": {"type": "string", "defaultValue": "cabin_lid.parquet"}}, "folder": {"name": "Bronze"}, "annotations": [], "type": "Pa<PERSON><PERSON>", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "fileName": {"value": "@dataset().tablename", "type": "Expression"}, "folderPath": "lakehouse/parquet/landing-zone/radixx", "fileSystem": "synapse"}, "compressionCodec": "snappy"}, "schema": []}}