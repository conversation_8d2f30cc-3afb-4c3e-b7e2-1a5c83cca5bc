{"name": "metadatawatermarks", "properties": {"linkedServiceName": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "annotations": [], "type": "Pa<PERSON><PERSON>", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "fileName": "watermarks.parquet", "folderPath": "lakehouse/parquet/metadata", "fileSystem": "synapse"}, "compressionCodec": "snappy"}, "schema": [{"name": "table_name", "type": "UTF8"}, {"name": "last_watermark", "type": "INT96"}, {"name": "updated_at", "type": "INT96"}]}}