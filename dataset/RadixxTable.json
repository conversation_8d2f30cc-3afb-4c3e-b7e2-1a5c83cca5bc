{"name": "RadixxTable", "properties": {"linkedServiceName": {"referenceName": "CipherwaveRadixx", "type": "LinkedServiceReference", "parameters": {"keyvaultBaseUrl": "https://dev-san-kv-lakehouse-001.vault.azure.net/"}}, "parameters": {"keyvaultBaseUrl": {"type": "string", "defaultValue": "https://dev-san-kv-lakehouse-001.vault.azure.net/"}}, "annotations": [], "type": "SqlServerTable", "schema": []}}