{"name": "RadixxCabinLid", "properties": {"linkedServiceName": {"referenceName": "CipherwaveRadixx", "type": "LinkedServiceReference"}, "folder": {"name": "Radixx"}, "annotations": [], "type": "SqlServerTable", "schema": [{"name": "logical_flight_id", "type": "decimal", "precision": 38, "scale": 0}, {"name": "departure_date", "type": "datetime2", "scale": 7}, {"name": "cabin", "type": "<PERSON><PERSON><PERSON>"}, {"name": "cabin_capacity", "type": "decimal", "precision": 38, "scale": 0}, {"name": "cabin_lid", "type": "decimal", "precision": 38, "scale": 0}, {"name": "created_by", "type": "<PERSON><PERSON><PERSON>"}, {"name": "created_date", "type": "datetime2", "scale": 7}, {"name": "last_modified_by", "type": "<PERSON><PERSON><PERSON>"}, {"name": "last_modified_date", "type": "datetime2", "scale": 7}], "typeProperties": {"schema": "dbo", "table": "cabin_lid"}}}