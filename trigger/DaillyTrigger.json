{"name": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"description": "A temporary daily trigger", "annotations": [], "runtimeState": "Started", "pipelines": [{"pipelineReference": {"referenceName": "FullRadixxPipeline", "type": "PipelineReference"}}], "type": "ScheduleTrigger", "typeProperties": {"recurrence": {"frequency": "Day", "interval": 1, "startTime": "2025-07-01T09:48:00", "timeZone": "South Africa Standard Time", "schedule": {"minutes": [15], "hours": [0]}}}}}