{"name": "CipherwaveRadixx", "properties": {"parameters": {"keyvaultBaseUrl": {"type": "string", "defaultValue": "https://dev-san-kv-lakehouse-001.vault.azure.net/"}, "userName": {"type": "string", "defaultValue": "<PERSON><PERSON><PERSON>"}, "secretName": {"type": "string", "defaultValue": "cpieters-cipherwave"}, "shirName": {"type": "string", "defaultValue": "dev-cipherwave-shir"}}, "annotations": [], "type": "SqlServer", "typeProperties": {"server": "**********", "database": "Radixx", "encrypt": "mandatory", "trustServerCertificate": true, "authenticationType": "SQL", "userName": "@{linkedService().userName}", "password": {"type": "AzureKeyVaultSecret", "store": {"referenceName": "AzureKey<PERSON>ault", "type": "LinkedServiceReference", "parameters": {"baseURL": {"value": "@linkedService().keyvaultBaseUrl", "type": "Expression"}}}, "secretName": {"value": "@linkedService().secretName", "type": "Expression"}}, "alwaysEncryptedSettings": {"alwaysEncryptedAkvAuthType": "ManagedIdentity"}}, "connectVia": {"referenceName": "dev-cipherwave-shir", "type": "IntegrationRuntimeReference"}}}