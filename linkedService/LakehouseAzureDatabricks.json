{"name": "LakehouseAzureDatabricks", "properties": {"parameters": {"keyvaultbaseURL": {"type": "string", "defaultValue": "https://dev-san-kv-lakehouse-001.vault.azure.net/"}, "clusterID": {"type": "string", "defaultValue": "0703-124105-oa6zhuqc"}, "databricksURL": {"type": "string", "defaultValue": "https://adb-293568896631603.3.azuredatabricks.net"}, "secretName": {"type": "string", "defaultValue": "lakehouse-databricks-access-token"}}, "annotations": [], "type": "AzureDatabricks", "typeProperties": {"domain": "@linkedService().databricksURL", "accessToken": {"type": "AzureKeyVaultSecret", "store": {"referenceName": "AzureKey<PERSON>ault", "type": "LinkedServiceReference", "parameters": {"baseURL": {"value": "@linkedService().keyvaultbaseURL", "type": "Expression"}}}, "secretName": {"value": "@linkedService().secretName", "type": "Expression"}}, "existingClusterId": "@linkedService().clusterID"}, "connectVia": {"referenceName": "AutoResolveIntegrationRuntime", "type": "IntegrationRuntimeReference"}}}