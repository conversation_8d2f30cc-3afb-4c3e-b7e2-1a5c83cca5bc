{"name": "dev-san-syn-lakehouse-001-WorkspaceDefaultSqlServer", "type": "Microsoft.Synapse/workspaces/linkedservices", "properties": {"typeProperties": {"connectionString": "Data Source=tcp:dev-san-syn-lakehouse-001.sql.azuresynapse.net,1433;Initial Catalog=@{linkedService().DBName}"}, "parameters": {"DBName": {"type": "String"}}, "type": "AzureSqlDW", "connectVia": {"referenceName": "AutoResolveIntegrationRuntime", "type": "IntegrationRuntimeReference"}, "annotations": []}}