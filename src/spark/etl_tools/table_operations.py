from delta.tables import DeltaTable
from pyspark.sql import Data<PERSON>rame, SparkSession


def resolve_external_root(spark: SparkSession, env: str, external_location: str) -> str:
    """Get the base URL for an external Unity Catalog location."""
    loc_df = spark.sql(f"DESCRIBE EXTERNAL LOCATION `{env}-{external_location}`")
    return loc_df.select("url").collect()[0][0]


def get_table_collection(
    spark: SparkSession,
    env: str,
    schema: str,
    list_of_tables: list,
    table_prefix: str = None,
) -> dict:
    """
    Get a collection of tables from the provided list.

    :param spark: Spark session object.
    :type spark: SparkSession
    :param env: Environment name (e.g., "dev", "prod").
    :type env: str
    :param schema: Database schema name in Unity Catalog.
    :type schema: str
    :param list_of_tables: List of table names to process.
    :type list_of_tables: list
    :param table_prefix: Optional table prefix
    :type table_prefix: str
    :return: Dictionary of Spark dataframes for each source table.
    :rtype: dict
    """

    tables_dict = {}

    for table_name in list_of_tables:
        try:
            df = read_delta(spark, env, schema, table_name, table_prefix)
        except ValueError:
            df = None
        tables_dict[table_name] = df

    return tables_dict


def read_delta(
    spark: SparkSession,
    env: str,
    schema: str,
    table_name: str,
    table_prefix: str = None,
) -> DataFrame:
    """
    Read DataFrame from Unity Catalog Delta table.

    :param spark: SparkSession for reading data
    :type spark: SparkSession
    :param env: Environment prefix (dev, prod)
    :type env: str
    :param schema: Database schema name in Unity Catalog
    :type schema: str
    :param table_name: Source table name
    :type table_name: str
    :param table_prefix: Optional table prefix
    :type table_prefix: str
    :type table_prefix: str

    :raises ValueError: If table does not exist
    :return: DataFrame containing table data
    :rtype: DataFrame
    """
    if table_prefix:
        target_catalog = f"{env}_lakehouse_catalog.{schema}.{table_prefix}_{table_name}"
    else:
        target_catalog = f"{env}_lakehouse_catalog.{schema}.{table_name}"

    if not spark.catalog.tableExists(target_catalog):
        raise ValueError(f"Table {target_catalog} does not exist")

    df = spark.table(target_catalog)
    print(f"Read {df.count()} rows from {target_catalog}")

    return df


def write_delta(
    df: DataFrame,
    env: str,
    schema: str,
    table_name: str,
    external_location: str,
    mode: str = "upsert",
    key_cols: list = None,
    optimize: bool = True,
) -> None:
    """
    Write DataFrame to Unity Catalog Delta table with multiple write modes.

    :param df: DataFrame to write to Delta table
    :type df: DataFrame
    :param env: Environment prefix (dev, prod)
    :type env: str
    :param schema: Database schema name in Unity Catalog
    :type schema: str
    :param table_name: Target table name
    :type table_name: str
    :param external_location: External location for the table
    :type external_location: str
    :param mode: Write mode - 'upsert', 'append', or 'overwrite'
    :type mode: str
    :param key_cols: List of key columns for upsert operations
    :type key_cols: list
    :param optimize: Whether to optimize table after write for Synapse compatibility
    :type optimize: bool
    :raises ValueError: If invalid mode is specified or key_cols missing for upsert
    :return: None
    :rtype: None
    """

    spark = df.sparkSession

    try:
        external_root = resolve_external_root(spark, env, external_location)
    except Exception as e:
        raise ValueError(f"Failed to resolve external root: {e}")

    if mode not in ["upsert", "append", "overwrite"]:
        raise ValueError(f"Invalid mode '{mode}'. Must be 'upsert', 'append', or 'overwrite'")

    if mode == "upsert" and not key_cols:
        raise ValueError("key_cols must be provided for upsert mode")

    target_catalog = f"{env}_lakehouse_catalog.{schema}.{table_name}"
    external_path = f"{external_root}/{schema}/{table_name}"

    table_exists = spark.catalog.tableExists(target_catalog)

    if not table_exists:
        # Create new table for all modes
        df.writeTo(target_catalog).using("delta").tableProperty("location", external_path).create()
        print(f"Created new table {target_catalog}")

    elif mode == "overwrite":
        # Overwrite existing table
        df.writeTo(target_catalog).using("delta").mode("overwrite").save()
        print(f"Overwrote table {target_catalog}")

    elif mode == "append":
        # Append to existing table
        df.writeTo(target_catalog).using("delta").mode("append").save()
        print(f"Appended data to {target_catalog}")

    elif mode == "upsert":
        # Merge (upsert) operation
        delta_tbl = DeltaTable.forName(spark, target_catalog)

        # Build join condition: t.k1 = s.k1 AND t.k2 = s.k2 ...
        cond = " AND ".join([f"t.{c} = s.{c}" for c in key_cols])

        delta_tbl.alias("t").merge(df.alias("s"), cond).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()

        print(f"Upserted data to {target_catalog}")

    # Optimize table to remove deletion vectors for Synapse compatibility
    if optimize and table_exists:
        delta_tbl = DeltaTable.forName(spark, target_catalog)
        delta_tbl.optimize().executeCompaction()
