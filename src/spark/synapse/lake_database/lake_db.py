from argparse import ArgumentPars<PERSON>

from pyspark.sql import SparkSession
from pyspark import SparkContext

from py4j.java_gateway import JavaObject


def generate_table_name(base_dir: str, target_dir: str) -> str:
    """
    Generates a table name by removing the base directory from the target directory
    and replacing path separators with underscores.

    :param base_dir: The base high-level Delta directory.
    :type base_dir: str
    :param target_dir: The full path to the subdirectory representing the table.
    :type target_dir: str
    :return: The generated table name.
    :rtype: str
    """
    if target_dir.startswith(base_dir):
        relative_path = target_dir[len(base_dir) :].lstrip("/")
    else:
        relative_path = target_dir
    table_name = relative_path.replace("/", "_").lower()
    return table_name


def find_delta_table_dirs(fs: JavaObject, sc: SparkContext, base_dir: str) -> list:
    """
    Traverses the base directory recursively and identifies directories that contain Delta tables.

    :param fs: The Hadoop FileSystem object.
    :type fs: org.apache.hadoop.fs.FileSystem
    :param sc: The SparkContext object.
    :type sc: SparkContext
    :param base_dir: The high-level Delta tables directory.
    :type base_dir: str
    :return: A list of directories containing Delta tables.
    :rtype: list
    :raises Exception: If an error occurs while accessing the directory.
    :return: None
    """
    delta_dirs = []

    def traverse(current_dir):
        try:
            path = sc._jvm.org.apache.hadoop.fs.Path(current_dir)
            if not fs.exists(path):
                print(f"Directory {current_dir} does not exist.")
                return
            files = fs.listStatus(path)
            has_delta = any(f.getPath().getName().startswith("_delta_log") for f in files)
            if has_delta:
                delta_dirs.append(current_dir)
            else:
                for f in files:
                    if f.isDirectory():
                        traverse(f.getPath().toString())
        except Exception as e:
            print(f"Error accessing directory {current_dir}: {e}")

    traverse(base_dir)
    return delta_dirs


def create_tables(spark: SparkSession, delta_table_dirs: list, database_name: str, base_dir: str):
    """
    Creates Delta tables based on the provided directories.

    :param spark: The Spark session object.
    :type spark: SparkSession
    :param delta_table_dirs: List of directories containing Delta tables.
    :type delta_table_dirs: list
    :param database_name: The target database name.
    :type database_name: str
    :param base_dir: The base directory for Delta tables.
    :type base_dir: str
    :return: None
    """
    for table_dir in delta_table_dirs:
        try:
            table_name = generate_table_name(base_dir, table_dir)
            create_table_query = f"""
            CREATE TABLE IF NOT EXISTS {database_name}.{table_name}
            USING DELTA
            LOCATION '{table_dir}'
            """
            spark.sql(create_table_query)
            print(f"Table '{database_name}.{table_name}' created at location '{table_dir}'.")
        except Exception as e:
            print(f"Error creating table for directory '{table_dir}': {e}")
            continue


def drop_tables(spark: SparkSession, delta_table_dirs: list, database_name: str, base_dir: str):
    """
    Drops Delta tables based on the provided directories.

    :param spark: The Spark session object.
    :type spark: SparkSession
    :param delta_table_dirs: List of directories containing Delta tables.
    :type delta_table_dirs: list
    :param database_name: The target database name.
    :type database_name: str
    :param base_dir: The base directory for Delta tables.
    :type base_dir: str
    :return: None
    """
    for table_dir in delta_table_dirs:
        try:
            table_name = generate_table_name(base_dir, table_dir)
            drop_table_query = f"DROP TABLE IF EXISTS {database_name}.{table_name}"
            spark.sql(drop_table_query)
            print(f"Table '{database_name}.{table_name}' has been dropped.")
        except Exception as e:
            print(f"Error dropping table '{database_name}.{table_name}': {e}")
            continue


def main():
    parser = ArgumentParser()
    parser.add_argument(
        "--action",
        type=str,
        default="create",
        help="Action to perform on the Delta tables (create or drop)",
    )
    parser.add_argument(
        "--database",
        type=str,
        default="bronze",
        help="Target database name",
    )
    args = parser.parse_args()

    spark = (
        SparkSession.builder.appName("LakeDB")
        .config("spark.sql.parquet.int96RebaseModeInWrite", "CORRECTED")
        .getOrCreate()
    )
    sc = spark.sparkContext

    # Access Hadoop FileSystem
    hadoop_conf = sc._jsc.hadoopConfiguration()
    fs = sc._jvm.org.apache.hadoop.fs.FileSystem.get(hadoop_conf)

    # Pull storage settings from Spark configuration
    try:
        storage_file_system = spark.conf.get("spark.storage.file_system")
        storage_account = spark.conf.get("spark.storage.storage_account")
        if not storage_file_system or not storage_account:
            raise ValueError("spark.storage.file_system or spark.storage.storage_account not set")
    except Exception as e:
        print(
            f"Missing Spark storage config: {e}. Use --conf spark.storage.file_system=... and --conf spark.storage.storage_account=..."
        )
        return

    action = args.action.lower()
    database_name = args.database

    delta_tables_dir = (
        f"abfss://{storage_file_system}@{storage_account}.dfs.core.windows.net/lakehouse/delta/{database_name}"
    )

    # Find all Delta table directories
    delta_table_dirs = find_delta_table_dirs(fs, sc, delta_tables_dir)

    if not delta_table_dirs:
        print(f"No Delta tables found in the directory: {delta_tables_dir}")
    else:
        print(f"Found {len(delta_table_dirs)} Delta tables to process.")

    try:
        create_db_query = f"CREATE DATABASE IF NOT EXISTS {database_name}"
        spark.sql(create_db_query)
        print(f"Database '{database_name}' ensured.")
    except Exception as e:
        print(f"Error creating database '{database_name}': {e}")
        raise

    if action == "create":
        create_tables(spark, delta_table_dirs, database_name, delta_tables_dir)
    elif action == "drop":
        drop_tables(spark, delta_table_dirs, database_name, delta_tables_dir)
    else:
        print(f"Unknown action '{action}'. Please set 'action' to 'create' or 'drop' in the config.")

    print("Delta table management script has completed its execution.")


if __name__ == "__main__":
    main()
