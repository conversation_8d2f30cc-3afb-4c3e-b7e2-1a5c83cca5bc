import argparse
import sys

from delta.tables import DeltaTable
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import (
    current_date,
    to_date,
    to_timestamp,
    hour,
    count,
    countDistinct,
    when,
    avg,
    datediff,
    sum as _sum,
    concat,
    lit,
    date_format,
    col,
    percentile_approx,
)

# Valid cabin_lid values; update this list if new cabin types are added
VALID_CABIN_LIDS = [165, 189]


def get_spark_session(
    app_name: str = None,
) -> SparkSession:
    """Create or retrieve a SparkSession."""
    return SparkSession.builder.appName(app_name).getOrCreate()


def read_source_tables(spark: SparkSession, env: str) -> tuple[DataFrame, ...]:
    """
    Read all necessary Radixx bronze tables from Unity Catalog.
    """
    segs = spark.table(f"{env}_lakehouse_catalog.bronze.radixx_reservation_segs")
    flight_segs = spark.table(f"{env}_lakehouse_catalog.bronze.radixx_flight_segments")
    fl_marketed = spark.table(f"{env}_lakehouse_catalog.bronze.radixx_fl_marketed_flights")
    cabin_lid = spark.table(f"{env}_lakehouse_catalog.bronze.radixx_cabin_lid")
    status_lk = spark.table(f"{env}_lakehouse_catalog.bronze.radixx_res_seg_status")
    channels = spark.table(f"{env}_lakehouse_catalog.bronze.radixx_res_channels")
    reservations = spark.table(f"{env}_lakehouse_catalog.bronze.radixx_reservations")
    person_org = spark.table(f"{env}_lakehouse_catalog.bronze.radixx_person_org")
    return (
        segs,
        flight_segs,
        fl_marketed,
        cabin_lid,
        status_lk,
        channels,
        reservations,
        person_org,
    )


def compute_quality_metrics(
    segs: DataFrame,
    flight_segs: DataFrame,
    fl_marketed: DataFrame,
    cabin_lid: DataFrame,
    status_lk: DataFrame,
    channels: DataFrame,
    reservations: DataFrame,
    person_org: DataFrame,
) -> DataFrame:
    """
    Compute daily quality and booking‐health metrics at the flight level,
    correctly joining and filtering for current/future flights only.
    """
    today = current_date()

    # Create segkey helper column and select specific columns to avoid ambiguity
    segs_with_key = segs.select(
        "confirmation_num",
        "record_num",
        "res_seg_status",
        "res_channel_id",
        "person_org_id",
        col("res_book_date").alias("reservation_book_date"),
    ).withColumn("segkey", concat(col("confirmation_num"), lit("_"), col("record_num")))

    # INNER JOIN with reservations on confirmation_num ONLY (reservations is PNR-level)
    df = segs_with_key.join(
        reservations.select("confirmation_num"),
        on="confirmation_num",
        how="inner",
    )

    # LEFT JOIN with person_org to get age data
    df = df.join(
        person_org.select("person_org_id", "age"),
        on="person_org_id",
        how="left",
    )

    # INNER JOIN with flight_segments on confirmation_num AND record_num
    df = df.join(
        flight_segs.select(
            "confirmation_num",
            "record_num",
            "logical_flight_id",
            col("departure_date").alias("segment_departure_date"),
        ),
        on=["confirmation_num", "record_num"],
        how="inner",
    )

    # INNER JOIN with fl_marketed_flights on logical_flight_id AND departure_date
    df = df.join(
        fl_marketed.select(
            "logical_flight_id",
            col("actual_depart_date_lt").alias("segment_departure_date"),  # Match alias
            col("flight_num").alias("flight_number"),
        ),
        on=["logical_flight_id", "segment_departure_date"],
        how="inner",
    )

    # LEFT JOIN with cabin_lid for capacity & cabin_lid validation
    df = df.join(
        cabin_lid.select(
            "logical_flight_id",
            col("departure_date").alias("segment_departure_date"),
            "cabin_lid",
        ),
        on=["logical_flight_id", "segment_departure_date"],
        how="left",
    )

    # LEFT JOIN with status & channel lookups
    df = df.join(
        status_lk.select("res_seg_status", col("res_seg_status_desc").alias("status_desc")),
        on="res_seg_status",
        how="left",
    )

    df = df.join(
        channels.select("res_channel_id", col("res_channel").alias("channel")),
        on="res_channel_id",
        how="left",
    )

    # Create final departure_date column and filter for current/future flights only
    df = df.withColumn("departure_date", to_date("segment_departure_date"))
    df = df.filter(col("departure_date") >= today)

    # Compute derived flags
    df = (
        df.withColumn(
            "days_to_departure",
            datediff(col("departure_date"), to_date(col("reservation_book_date"))),
        )
        .withColumn("booking_hour", hour(to_timestamp(col("reservation_book_date"))))
        .withColumn(
            "age_category",
            when(col("age").isNull(), lit("missing_age"))
            .when(col("age") < 18, lit("child"))
            .when((col("age") >= 18) & (col("age") <= 65), lit("adult"))
            .when((col("age") > 65) & (col("age") <= 100), lit("senior"))
            .otherwise(lit("invalid_age")),
        )
        .withColumn(
            "has_book_date",
            when(col("reservation_book_date").isNotNull(), 1).otherwise(0),
        )
        .withColumn("has_dep_date", when(col("departure_date").isNotNull(), 1).otherwise(0))
        .withColumn(
            "has_age",
            when(col("age").isNotNull(), 1).otherwise(0),
        )
        .withColumn("is_valid_status", when(col("status_desc").isNotNull(), 1).otherwise(0))
        .withColumn("is_cancel", when(col("status_desc") == "CANCELED", 1).otherwise(0))
        .withColumn("is_no_show", when(col("status_desc") == "NO SHOW", 1).otherwise(0))
        .withColumn(
            "is_valid_cabin",
            when(col("cabin_lid").isin(*VALID_CABIN_LIDS), 1).otherwise(0),
        )
        .withColumn("is_negative_days", when(col("days_to_departure") < 0, 1).otherwise(0))
        .withColumn("is_child", when(col("age_category") == "child", 1).otherwise(0))
        .withColumn("is_adult", when(col("age_category") == "adult", 1).otherwise(0))
        .withColumn("is_senior", when(col("age_category") == "senior", 1).otherwise(0))
        .withColumn("is_invalid_age", when(col("age_category") == "invalid_age", 1).otherwise(0))
        .withColumn(
            "is_missing_age",
            when(col("age_category") == "missing_age", 1).otherwise(0),
        )
    )

    # Aggregate into final metrics
    metrics = (
        df.groupBy("departure_date", "flight_number")
        .agg(
            count("*").alias("total_segments"),
            countDistinct("confirmation_num").alias("distinct_reservations"),
            (100 * avg("has_book_date")).alias("pct_segments_with_book_date"),
            (100 * avg("has_dep_date")).alias("pct_segments_with_departure_date"),
            (100 * avg("has_age")).alias("pct_segments_with_age"),
            (100 * (1 - avg("is_valid_status"))).alias("pct_invalid_status"),
            (100 * avg("is_cancel")).alias("cancel_rate"),
            (100 * avg("is_no_show")).alias("no_show_rate"),
            avg(col("days_to_departure")).alias("avg_lead_time_days"),
            percentile_approx(col("days_to_departure"), 0.50).alias("median_lead_time_days"),
            (100 * avg(when(col("days_to_departure") <= 7, 1).otherwise(0))).alias("pct_bookings_last_7d"),
            (
                100
                * avg(
                    when(
                        (col("days_to_departure") > 7) & (col("days_to_departure") <= 30),
                        1,
                    ).otherwise(0)
                )
            ).alias("pct_bookings_7_30d"),
            (100 * avg(when(col("days_to_departure") > 30, 1).otherwise(0))).alias("pct_bookings_over_30d"),
            (count("*") / countDistinct("confirmation_num")).alias("avg_segments_per_res"),
            (100 * avg("is_valid_cabin")).alias("pct_valid_cabin_lid"),
            (100 * avg("is_negative_days")).alias("pct_negative_days"),
            (100 * avg(when(col("booking_hour") < 12, 1).otherwise(0))).alias("pct_morning_bookings"),
            (100 * avg(when((col("booking_hour") >= 12) & (col("booking_hour") < 18), 1).otherwise(0))).alias(
                "pct_afternoon_bookings"
            ),
            (100 * avg(when(col("booking_hour") >= 18, 1).otherwise(0))).alias("pct_evening_bookings"),
            (100 * avg("is_child")).alias("pct_children"),
            (100 * avg("is_adult")).alias("pct_adults"),
            (100 * avg("is_senior")).alias("pct_seniors"),
            (100 * avg("is_invalid_age")).alias("pct_invalid_age"),
            (100 * avg("is_missing_age")).alias("pct_missing_age"),
        )
        .withColumn("flight_date", col("departure_date"))
        .withColumn(
            "flight_id",
            concat(
                col("flight_number"),
                lit("_"),
                date_format(col("departure_date"), "yyyyMMdd"),
            ),
        )
        .withColumn("record_date", today)
        .select(
            "flight_date",
            "flight_id",
            "record_date",
            "total_segments",
            "distinct_reservations",
            "pct_segments_with_book_date",
            "pct_segments_with_departure_date",
            "pct_segments_with_age",
            "pct_invalid_status",
            "cancel_rate",
            "no_show_rate",
            "avg_lead_time_days",
            "median_lead_time_days",
            "pct_bookings_last_7d",
            "pct_bookings_7_30d",
            "pct_bookings_over_30d",
            "avg_segments_per_res",
            "pct_valid_cabin_lid",
            "pct_negative_days",
            "pct_morning_bookings",
            "pct_afternoon_bookings",
            "pct_evening_bookings",
            "pct_children",
            "pct_adults",
            "pct_seniors",
            "pct_invalid_age",
            "pct_missing_age",
        )
    )

    return metrics


def resolve_external_root(spark: SparkSession, env: str, external_location: str) -> str:
    """Get the base URL for an external Unity Catalog location."""
    loc_df = spark.sql(f"DESCRIBE EXTERNAL LOCATION `{env}-{external_location}`")
    return loc_df.select("url").collect()[0][0]


def write_delta(
    metrics_df: DataFrame,
    env: str,
    schema: str,
    table_name: str,
    external_root: str,
    key_cols: list = ["flight_id", "record_date"],
) -> None:
    """Upsert metrics DataFrame to Unity Catalog Delta table."""
    spark = metrics_df.sparkSession
    target_catalog = f"{env}_lakehouse_catalog.{schema}.{table_name}"
    external_path = f"{external_root}/{schema}/{table_name}"

    # create table if it doesn't exist
    if not spark.catalog.tableExists(target_catalog):
        metrics_df.writeTo(target_catalog).using("delta").tableProperty("location", external_path).create()
        print(f"Created new table {target_catalog}")
        return

    # otherwise merge (upsert)
    delta_tbl = DeltaTable.forName(spark, target_catalog)

    # build join condition: t.k1 = s.k1 AND t.k2 = s.k2 ...
    cond = " AND ".join([f"t.{c} = s.{c}" for c in key_cols])

    delta_tbl.alias("t").merge(metrics_df.alias("s"), cond).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()

    print(f"Upserted data to {target_catalog}")

    # Optimize table to remove deletion vectors for Synapse compatibility
    delta_tbl.optimize().executeCompaction()


def parse_args(argv: list) -> argparse.Namespace:
    """Parse arguments in Databricks mode."""
    parser = argparse.ArgumentParser(description="Compute daily reservation quality metrics")
    parser.add_argument(
        "--env",
        choices=["dev", "prod"],
        default="dev",
        required=True,
        help="Environment to target (dev or prod)",
    )
    return parser.parse_args(" ".join(argv).split())


def main() -> None:
    """Orchestrate the metric computation and write-out."""
    args = parse_args(sys.argv[1:])
    spark = get_spark_session(app_name="BronzeReservationQualityMetrics")

    (
        segs,
        flight_segs,
        fl_marketed,
        cabin_lid,
        status_lk,
        channels,
        reservations,
        person_org,
    ) = read_source_tables(spark, args.env)

    metrics_df = compute_quality_metrics(
        segs,
        flight_segs,
        fl_marketed,
        cabin_lid,
        status_lk,
        channels,
        reservations,
        person_org,
    )

    external_root = resolve_external_root(spark, args.env, "lakehouse-data")
    write_delta(
        metrics_df,
        args.env,
        schema="metrics",
        table_name="bronze_reservation_quality",
        external_root=external_root,
    )
    print("Quality metrics processing complete.")


if __name__ == "__main__":
    main()
