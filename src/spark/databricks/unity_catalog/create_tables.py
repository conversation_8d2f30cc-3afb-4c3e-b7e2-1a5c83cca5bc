from argparse import ArgumentParser
import sys
from pyspark.sql import SparkSession
from pyspark.sql import DataFrame


def generate_table_name(base_dir: str, target_dir: str) -> str:
    # Same logic as lake_db
    if target_dir.startswith(base_dir):
        relative = target_dir[len(base_dir) :].lstrip("/")
    else:
        relative = target_dir
    return relative.replace("/", "_").lower()


def get_spark_session() -> SparkSession:
    """Create and return a Spark session with Azure support"""
    return (
        SparkSession.builder.appName("UC_Creator")
        .enableHiveSupport()
        .config(
            "spark.jars.packages",
            "org.apache.hadoop:hadoop-azure:3.3.4,com.microsoft.azure:azure-storage:8.6.6",
        )
        .getOrCreate()
    )


def write_table_to_catalog(
    spark: SparkSession,
    df: DataFrame,
    env: str,
    schema: str,
    table_name: str,
) -> None:
    """Register or replace table in Unity Catalog using DataFrameWriterV2"""
    df.writeTo(f"{env}_lakehouse_catalog.{schema}.{table_name}").using("delta").createOrReplace()


def main():
    parser = ArgumentParser()
    parser.add_argument("--env", choices=["dev", "prod"], default="dev", help="Environment prefix (dev or prod)")
    parser.add_argument("--database", type=str, required=True, help="Unity Catalog schema name")
    parser.add_argument(
        "--table-prefix",
        type=str,
        default="",
        help="Optional top-level folder under schema (empty for direct schema access)",
    )
    parser.add_argument("--tables", type=str, required=True, help="Comma-separated list of table directory names")
    args = parser.parse_args(" ".join(sys.argv[1:]).split())

    env = args.env.lower()
    schema_name = args.database
    prefix = args.table_prefix.rstrip("/") if args.table_prefix else ""
    table_list = [t.strip() for t in args.tables.split(",") if t.strip()]

    spark = get_spark_session()
    # Build base directory for Delta tables (container is always 'synapse')
    storage_fs = "synapse"
    storage_account = f"{env}sanstlakehouse001.dfs.core.windows.net"

    # Handle both prefixed and non-prefixed scenarios
    if prefix:
        base_path = f"abfss://{storage_fs}@{storage_account}/lakehouse/delta/{schema_name}/{prefix}"
    else:
        base_path = f"abfss://{storage_fs}@{storage_account}/lakehouse/delta/{schema_name}"

    print(f"Registering tables {table_list} under {base_path}")

    # Register specified tables with or without prefix
    for tbl in table_list:
        path = f"{base_path}/{tbl}"
        full_tbl = f"{prefix}_{tbl}" if prefix else tbl
        try:
            df = spark.read.format("delta").load(path)
            write_table_to_catalog(spark, df, env, schema_name, full_tbl)
            print(f"Created table {schema_name}.{full_tbl}")
        except Exception as e:
            print(f"Error creating table for {path}: {e}")

    print("Unity Catalog table registration complete.")


if __name__ == "__main__":
    main()
