import argparse
import sys

from pyspark.sql import SparkSession
from pyspark.sql import DataFrame
from pyspark.sql import functions as F

from etl_tools.table_operations import get_table_collection, write_delta


def parse_args(argv: list) -> argparse.Namespace:
    """Parse arguments in Databricks mode."""
    parser = argparse.ArgumentParser(description="ETL for dim_agent table")
    parser.add_argument(
        "--env",
        choices=["dev", "prod"],
        default="dev",
        required=True,
        help="Environment to target (dev or prod)",
    )
    return parser.parse_args(" ".join(argv).split())


def get_spark_session(
    app_name: str = None,
) -> SparkSession:
    """Create or retrieve a SparkSession."""
    return SparkSession.builder.appName(app_name).getOrCreate()


def build_calculated_table(spark: SparkSession, tables_dict: dict) -> DataFrame:
    """Build the calculated table - dim_agent.
    :param spark: Spark session object.
    :type spark: SparkSession
    :param tables_dict: Dictionary of source tables as DataFrames.
    :type tables_dict: dict
    :return: DataFrame for the dim_agent table.
    :rtype: DataFrame
    """

    # read booking_agent from both reservations and reservation_segs
    res_agents = tables_dict["reservations"].select(F.col("booking_agent").alias("agent_key"))
    seg_agents = tables_dict["reservation_segs"].select(F.col("booking_agent").alias("agent_key"))

    # remove duplicates and nulls
    if res_agents is None or seg_agents is None:
        print("One or more source tables do not exist. Cannot build dim_agent table.")
        return None

    # ensure both DataFrames have the same schema
    res_agents = res_agents.withColumn("agent_key", F.col("agent_key").cast("string"))
    seg_agents = seg_agents.withColumn("agent_key", F.col("agent_key").cast("string"))

    # combine both DataFrames, remove duplicates, and filter out nulls
    agents_df = res_agents.union(seg_agents).distinct().filter(F.col("agent_key").isNotNull())

    # union, dedupe and filter nulls
    dim_agent_df = res_agents.union(seg_agents).distinct().filter(F.col("agent_key").isNotNull())

    # for now use the key itself as display name
    dim_agent_df = agents_df.withColumn("agent_name", F.upper(F.col("agent_key")))

    return dim_agent_df


def main():
    """Main function to execute the ETL process for the dim_agent table."""

    args = parse_args(sys.argv[1:])
    spark = get_spark_session("DimAgentBuilder")

    # all the source tables we need
    source_table_names = ["reservations", "reservation_segs"]

    tables_dict = get_table_collection(spark, args.env, "bronze", source_table_names, table_prefix="radixx")

    # if any of our tables are None, we cannot build the dim_agent table
    if any(df is None for df in tables_dict.values()):
        print("One or more source tables do not exist. Cannot build dim_agent table.")
        return

    calculated_df = build_calculated_table(spark, tables_dict)

    # our calculated DataFrame is ready, write it to the silver layer
    if calculated_df is not None:
        write_delta(
            calculated_df,
            env=args.env,
            schema="silver",
            table_name="dim_agent",
            external_location="lakehouse-data",
            mode="upsert",
            key_cols=["agent_key"],
        )
    else:
        print("Failed to build calculated DataFrame. ETL process aborted.")

    spark.stop()


if __name__ == "__main__":
    main()
