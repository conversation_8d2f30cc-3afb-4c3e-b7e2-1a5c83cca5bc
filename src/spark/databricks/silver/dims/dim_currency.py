import argparse
import sys

from pyspark.sql import SparkSession
from pyspark.sql import DataFrame
from pyspark.sql import functions as F

from etl_tools.table_operations import get_table_collection, write_delta


def parse_args(argv: list):
    """Parse arguments in Databricks mode."""
    parser = argparse.ArgumentParser(description="ETL for dim_currency table")
    parser.add_argument(
        "--env",
        choices=["dev", "prod"],
        default="dev",
        required=True,
        help="Environment to target (dev or prod)",
    )
    return parser.parse_args(" ".join(argv).split())


def get_spark_session(app_name: str = None) -> SparkSession:
    """Create or retrieve a SparkSession."""
    return SparkSession.builder.appName(app_name).getOrCreate()


def build_calculated_table(spark: SparkSession, tables_dict: dict) -> DataFrame:
    """Build the calculated table - dim_currency.
    :param spark: Spark session object.
    :type spark: SparkSession
    :param tables_dict: Dictionary of source tables as DataFrames.
    :type tables_dict: dict
    :return: DataFrame for the dim_currency table.
    :rtype: DataFrame
    """
    # read currency codes from reservations, charges, and payments
    res_cur = tables_dict["reservations"].select(F.col("res_currency").alias("currency_code"))
    charges_cur = tables_dict["res_charges"].select(F.col("currency_code"))

    # union sources, drop duplicates and nulls, ensure string type
    currencies_df = (
        res_cur.union(charges_cur)
        .distinct()
        .filter(F.col("currency_code").isNotNull())
        .withColumn("currency_code", F.col("currency_code").cast("string"))
    )

    # use code as name and symbol
    dim_currency_df = currencies_df.select(
        F.col("currency_code"),
        F.col("currency_code").alias("currency_name"),
        F.col("currency_code").alias("symbol"),
    )

    return dim_currency_df


def main():
    """Main function to execute the ETL process for the dim_currency table."""
    args = parse_args(sys.argv[1:])
    spark = get_spark_session("DimCurrencyBuilder")

    source_table_names = ["reservations", "res_charges"]
    tables_dict = get_table_collection(spark, args.env, "bronze", source_table_names, table_prefix="radixx")
    if any(df is None for df in tables_dict.values()):
        print("One or more source tables do not exist. Cannot build dim_currency table.")
        return

    dim_currency_df = build_calculated_table(spark, tables_dict)
    if dim_currency_df is not None:
        write_delta(
            dim_currency_df,
            env=args.env,
            schema="silver",
            table_name="dim_currency",
            external_location="lakehouse-data",
            mode="upsert",
            key_cols=["currency_code"],
        )
    else:
        print("Failed to build calculated DataFrame. ETL process aborted.")

    spark.stop()


if __name__ == "__main__":
    main()
