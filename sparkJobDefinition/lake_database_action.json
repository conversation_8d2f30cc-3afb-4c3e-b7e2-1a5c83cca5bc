{"name": "lake_database_action", "properties": {"folder": {"name": "LakeDatabase"}, "targetBigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "requiredSparkVersion": "3.4", "language": "python", "scanFolder": false, "jobProperties": {"name": "lake_database_action", "file": "abfss://<EMAIL>/synapse/workspaces/dev-san-syn-lakehouse-001/batchjobs/lake_database_action/lake_db.py", "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "1", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "527b8e90-2acd-40b9-8c06-9689a7870337", "spark.synapse.context.sjdname": "lake_database_action"}, "args": ["--action", "drop", "--database", "bronze"], "jars": [], "pyFiles": [""], "files": [], "driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2}}}