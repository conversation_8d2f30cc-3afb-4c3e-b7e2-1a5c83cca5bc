{"name": "WatermarksDeltaToJson", "properties": {"folder": {"name": "Watermarks"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "DeltaSource"}], "sinks": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "JsonSink"}], "transformations": [], "scriptLines": ["source(allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     format: 'delta',", "     fileSystem: 'synapse',", "     folderPath: 'lakehouse/delta/metadata/watermarks') ~> DeltaSource", "DeltaSource sink(allowSchemaDrift: true,", "     validateSchema: false,", "     format: 'json',", "     fileSystem: 'synapse',", "     folderPath: 'lakehouse/json/metadata',", "     truncate: true,", "     partitionFileNames:['watermarks.json'],", "     umask: 0022,", "     preCommands: [],", "     postCommands: [],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true,", "     partitionBy('hash', 1)) ~> JsonSink"]}}}