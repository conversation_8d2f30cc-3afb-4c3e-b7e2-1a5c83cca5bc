{"name": "WatermarksDeltaToParquet", "properties": {"folder": {"name": "Watermarks"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "DeltaSource"}], "sinks": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "ParquetSink"}], "transformations": [], "scriptLines": ["source(allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     format: 'delta',", "     fileSystem: 'synapse',", "     folderPath: 'lakehouse/delta/metadata/watermarks') ~> DeltaSource", "DeltaSource sink(allowSchemaDrift: true,", "     validateSchema: false,", "     format: 'parquet',", "     fileSystem: 'synapse',", "     folderPath: 'lakehouse/parquet/metadata',", "     truncate: true,", "     partitionFileNames:['watermarks.parquet'],", "     umask: 0022,", "     preCommands: [],", "     postCommands: [],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true,", "     partitionBy('hash', 1)) ~> ParquetSink"]}}}