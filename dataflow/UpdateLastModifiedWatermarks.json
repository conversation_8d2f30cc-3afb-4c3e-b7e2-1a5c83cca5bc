{"name": "UpdateLastModifiedWatermarks", "properties": {"folder": {"name": "Watermarks"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "BronzeDeltaSource"}], "sinks": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "UpdateWatermark"}], "transformations": [{"name": "GetLastModified"}, {"name": "UpdatedAt"}, {"name": "UpsertConditional"}], "scriptLines": ["parameters{", "     tableName as string (\"cabin_lid\"),", "     db as string (\"radixx\"),", "     waterMarkColumn as string (\"last_modified_date\")", "}", "source(allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     format: 'delta',", "     fileSystem: 'synapse',", "     folderPath: (concat('lakehouse/delta/bronze/',$db, '/', $tableName))) ~> BronzeDeltaSource", "UpdatedAt aggregate(groupBy(table_name,", "          updated_at),", "     last_watermark = max(toString(byName($waterMarkColumn)))) ~> GetLastModified", "BronzeDeltaSource derive(updated_at = currentTimestamp(),", "          table_name = $tableName) ~> UpdatedAt", "GetLastModified alterRow(upsertIf(not(isNull(last_watermark)))) ~> UpsertConditional", "UpsertConditional sink(allowSchemaDrift: true,", "     validateSchema: false,", "     format: 'delta',", "     fileSystem: 'synapse',", "     folderPath: 'lakehouse/delta/metadata/watermarks',", "     mergeSchema: false,", "     autoCompact: false,", "     optimizedWrite: false,", "     vacuum: 0,", "     deletable: false,", "     insertable: true,", "     updateable: false,", "     upsertable: true,", "     keys:['table_name'],", "     umask: 0022,", "     preCommands: [],", "     postCommands: [],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true) ~> UpdateWatermark"]}}}