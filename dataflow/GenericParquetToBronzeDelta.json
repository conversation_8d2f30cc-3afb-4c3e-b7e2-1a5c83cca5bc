{"name": "GenericParquetToBronzeDelta", "properties": {"folder": {"name": "ParquetToDelta"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "ParquetSource"}], "sinks": [{"linkedService": {"referenceName": "dev-san-syn-lakehouse-001-WorkspaceDefaultStorage", "type": "LinkedServiceReference"}, "name": "BronzeDeltaSink"}], "transformations": [{"name": "UpsertCondition"}], "scriptLines": ["parameters{", "     source as string (\"cabin_lid\"),", "     db as string (\"radixx\"),", "     upsert_column_names as string[] ([\"logical_flight_id\"])", "}", "source(allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     format: 'parquet',", "     fileSystem: 'synapse',", "     folderPath: (concat('lakehouse/parquet/landing-zone/', $db)),", "     fileName: (concat($source, '.parquet')),", "     mode: 'read') ~> ParquetSource", "ParquetSource alterRow(upsertIf(true())) ~> UpsertCondition", "UpsertCondition sink(allowSchemaDrift: true,", "     validateSchema: false,", "     format: 'delta',", "     fileSystem: 'synapse',", "     folderPath: (concat('lakehouse/delta/bronze/',$db, '/', $source)),", "     mergeSchema: false,", "     autoCompact: false,", "     optimizedWrite: false,", "     vacuum: 0,", "     deletable: false,", "     insertable: true,", "     updateable: false,", "     upsertable: true,", "     keys:($upsert_column_names),", "     umask: 0022,", "     preCommands: [],", "     postCommands: [],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true) ~> BronzeDeltaSink"]}}}