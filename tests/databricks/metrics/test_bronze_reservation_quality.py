import sys
import pytest
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType

from src.spark.databricks.metrics.bronze_reservation_quality import (
    compute_quality_metrics,
)


@pytest.fixture(scope="session")
def spark():
    """Local SparkSession for pytest (no Databricks Connect)."""
    python_exec = sys.executable
    spark = (
        SparkSession.builder.master("local[2]")
        .appName("pytests")
        .config("spark.pyspark.python", python_exec)
        .config("spark.pyspark.driver.python", python_exec)
        .config("spark.sql.adaptive.enabled", "false")
        .config("spark.sql.adaptive.coalescePartitions.enabled", "false")
        .getOrCreate()
    )
    yield spark
    spark.stop()


def get_segs_schema():
    return StructType(
        [
            StructField("confirmation_num", StringType(), True),
            StructField("record_num", IntegerType(), True),
            StructField("res_seg_status", StringType(), True),
            StructField("res_channel_id", IntegerType(), True),
            StructField("person_org_id", IntegerType(), True),
            StructField("res_book_date", StringType(), True),
        ]
    )


def get_reservations_schema():
    return StructType(
        [
            StructField("confirmation_num", StringType(), True),
        ]
    )


def get_person_org_schema():
    return StructType(
        [
            StructField("person_org_id", IntegerType(), True),
            StructField("age", IntegerType(), True),
        ]
    )


def get_flight_segs_schema():
    return StructType(
        [
            StructField("confirmation_num", StringType(), True),
            StructField("record_num", IntegerType(), True),
            StructField("logical_flight_id", StringType(), True),
            StructField("departure_date", StringType(), True),
        ]
    )


def get_fl_marketed_schema():
    return StructType(
        [
            StructField("logical_flight_id", StringType(), True),
            StructField("actual_depart_date_lt", StringType(), True),
            StructField("flight_num", StringType(), True),
        ]
    )


def get_cabin_lid_schema():
    return StructType(
        [
            StructField("logical_flight_id", StringType(), True),
            StructField("departure_date", StringType(), True),
            StructField("cabin_lid", IntegerType(), True),
        ]
    )


def get_status_lk_schema():
    return StructType(
        [
            StructField("res_seg_status", StringType(), True),
            StructField("res_seg_status_desc", StringType(), True),
        ]
    )


def get_channels_schema():
    return StructType(
        [
            StructField("res_channel_id", IntegerType(), True),
            StructField("res_channel", StringType(), True),
        ]
    )


def test_compute_quality_metrics_all_null_ages(spark):
    """Test when all ages are null."""
    today = spark.sql("SELECT date_format(current_date(), 'yyyy-MM-dd') AS dt").first()[
        "dt"
    ]

    segs = spark.createDataFrame(
        [
            ("PNR001", 1, "CONFIRMED", 100, 1001, "2025-07-10 10:00:00"),
            ("PNR001", 2, "CONFIRMED", 100, 1002, "2025-07-10 10:00:00"),
            ("PNR002", 1, "CONFIRMED", 101, 1003, "2025-07-10 11:00:00"),
        ],
        schema=get_segs_schema(),
    )

    reservations = spark.createDataFrame(
        [("PNR001",), ("PNR002",)], schema=get_reservations_schema()
    )

    person_org = spark.createDataFrame(
        [(1001, None), (1002, None), (1003, None)], schema=get_person_org_schema()
    )

    flight_segs = spark.createDataFrame(
        [
            ("PNR001", 1, "FLT001", today),
            ("PNR001", 2, "FLT001", today),
            ("PNR002", 1, "FLT001", today),
        ],
        schema=get_flight_segs_schema(),
    )

    fl_marketed = spark.createDataFrame(
        [("FLT001", today, "FA123")], schema=get_fl_marketed_schema()
    )

    cabin_lid = spark.createDataFrame(
        [("FLT001", today, 165)], schema=get_cabin_lid_schema()
    )

    status_lk = spark.createDataFrame(
        [("CONFIRMED", "CONFIRMED")], schema=get_status_lk_schema()
    )

    channels = spark.createDataFrame(
        [(100, "WEB"), (101, "AGENCY")], schema=get_channels_schema()
    )

    result = compute_quality_metrics(
        segs,
        flight_segs,
        fl_marketed,
        cabin_lid,
        status_lk,
        channels,
        reservations,
        person_org,
    )

    assert result.count() >= 1
    row = result.collect()[0]
    assert row["pct_missing_age"] == 100.0


def test_compute_quality_metrics_comprehensive(spark):
    """Comprehensive edge-case scenarios."""
    today = spark.sql("SELECT date_format(current_date(), 'yyyy-MM-dd') AS dt").first()[
        "dt"
    ]

    segs = spark.createDataFrame(
        [
            ("PNR001", 1, "CONFIRMED", 100, 1001, "2025-07-10 08:00:00"),
            ("PNR002", 1, "CANCELED", 100, 1002, "2025-07-10 14:00:00"),
            ("PNR003", 1, "CONFIRMED", 101, 1003, "2025-07-20 10:00:00"),
            ("PNR004", 1, "CONFIRMED", 100, None, "2025-07-10 20:00:00"),
            ("PNR005", 1, None, 100, 1005, "2025-07-10 10:00:00"),
            ("PNR006", 1, "CONFIRMED", 100, 1006, None),
        ],
        schema=get_segs_schema(),
    )

    reservations = spark.createDataFrame(
        [(f"PNR00{i}",) for i in range(1, 7)], schema=get_reservations_schema()
    )

    person_org = spark.createDataFrame(
        [
            (1001, 30),
            (1002, 15),
            (1003, 150),
            (1005, -5),
            (1006, None),
        ],
        schema=get_person_org_schema(),
    )

    flight_segs = spark.createDataFrame(
        [(f"PNR00{i}", 1, "FLT001", today) for i in range(1, 7)],
        schema=get_flight_segs_schema(),
    )

    fl_marketed = spark.createDataFrame(
        [("FLT001", today, "FA123")], schema=get_fl_marketed_schema()
    )

    cabin_lid = spark.createDataFrame(
        [("FLT001", today, 165)], schema=get_cabin_lid_schema()
    )

    status_lk = spark.createDataFrame(
        [
            ("CONFIRMED", "CONFIRMED"),
            ("CANCELED", "CANCELED"),
        ],
        schema=get_status_lk_schema(),
    )

    channels = spark.createDataFrame(
        [(100, "WEB"), (101, "AGENCY")], schema=get_channels_schema()
    )

    result = compute_quality_metrics(
        segs,
        flight_segs,
        fl_marketed,
        cabin_lid,
        status_lk,
        channels,
        reservations,
        person_org,
    )

    rows = result.collect()
    assert rows, "Expected ≥1 row"

    r = rows[0]
    assert r["total_segments"] == 6
    assert r["distinct_reservations"] == 6
    assert pytest.approx(r["pct_segments_with_book_date"], rel=1e-2) == 83.33
    assert pytest.approx(r["pct_segments_with_age"], rel=1e-2) == 66.67
    assert pytest.approx(r["cancel_rate"], rel=1e-2) == 16.67
    assert pytest.approx(r["pct_invalid_status"], rel=1e-2) == 16.67
    assert pytest.approx(r["pct_valid_cabin_lid"], rel=1e-2) == 100.0
    assert r["pct_morning_bookings"] > 0
    assert r["pct_afternoon_bookings"] > 0
    assert r["pct_evening_bookings"] > 0


def test_compute_quality_metrics_basic_validation(spark):
    """Smoke test for single‑segment flight."""
    today = spark.sql("SELECT date_format(current_date(), 'yyyy-MM-dd') AS dt").first()[
        "dt"
    ]

    segs = spark.createDataFrame(
        [("PNR001", 1, "CONFIRMED", 100, 1001, "2025-07-10 10:00:00")],
        schema=get_segs_schema(),
    )

    reservations = spark.createDataFrame(
        [("PNR001",)], schema=get_reservations_schema()
    )

    person_org = spark.createDataFrame([(1001, 30)], schema=get_person_org_schema())

    flight_segs = spark.createDataFrame(
        [("PNR001", 1, "FLT001", today)], schema=get_flight_segs_schema()
    )

    fl_marketed = spark.createDataFrame(
        [("FLT001", today, "FA123")], schema=get_fl_marketed_schema()
    )

    cabin_lid = spark.createDataFrame(
        [("FLT001", today, 165)], schema=get_cabin_lid_schema()
    )

    status_lk = spark.createDataFrame(
        [("CONFIRMED", "CONFIRMED")], schema=get_status_lk_schema()
    )

    channels = spark.createDataFrame([(100, "WEB")], schema=get_channels_schema())

    result = compute_quality_metrics(
        segs,
        flight_segs,
        fl_marketed,
        cabin_lid,
        status_lk,
        channels,
        reservations,
        person_org,
    )

    assert result.count() == 1
    r = result.collect()[0]
    assert r["total_segments"] == 1
    assert r["distinct_reservations"] == 1
