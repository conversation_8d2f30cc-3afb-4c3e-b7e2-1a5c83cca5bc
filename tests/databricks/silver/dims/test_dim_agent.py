import sys
import pytest
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, IntegerType

from src.spark.databricks.silver.dims.dim_agent import build_calculated_table


@pytest.fixture(scope="session")
def spark():
    """Local SparkSession for pytest (no Databricks Connect)."""
    python_exec = sys.executable
    spark = (
        SparkSession.builder.master("local[2]")
        .appName("pytests")
        .config("spark.pyspark.python", python_exec)
        .config("spark.pyspark.driver.python", python_exec)
        .config("spark.sql.adaptive.enabled", "false")
        .config("spark.sql.adaptive.coalescePartitions.enabled", "false")
        .getOrCreate()
    )
    yield spark
    spark.stop()


def get_reservations_schema():
    """Schema for reservations table."""
    return StructType(
        [
            StructField("confirmation_num", StringType(), True),
            <PERSON>ruct<PERSON>ield("booking_agent", StringType(), True),
        ]
    )


def get_reservation_segs_schema():
    """Schema for reservation_segs table."""
    return StructType(
        [
            StructField("confirmation_num", StringType(), True),
            StructField("record_num", IntegerType(), True),
            StructField("booking_agent", StringType(), True),
        ]
    )


def test_build_calculated_table_basic_functionality(spark):
    """Test basic functionality with simple data."""

    # Create test data
    reservations = spark.createDataFrame(
        [
            ("PNR001", "WEB"),
            ("PNR002", "AGENCY"),
            ("PNR003", "CALL_CENTER"),
        ],
        schema=get_reservations_schema(),
    )

    reservation_segs = spark.createDataFrame(
        [
            ("PNR001", 1, "WEB"),
            ("PNR002", 1, "AGENCY"),
            ("PNR003", 1, "CALL_CENTER"),
        ],
        schema=get_reservation_segs_schema(),
    )

    tables_dict = {"reservations": reservations, "reservation_segs": reservation_segs}

    result = build_calculated_table(spark, tables_dict)

    # Verify result
    assert result is not None, "Result should not be None"
    assert result.count() == 3, "Should have 3 distinct agents"

    # Collect results and verify content
    result_rows = result.collect()
    agent_keys = [row["agent_key"] for row in result_rows]
    agent_names = [row["agent_name"] for row in result_rows]

    # Check that all expected agents are present
    expected_agents = {"WEB", "AGENCY", "CALL_CENTER"}
    actual_agents = set(agent_keys)
    assert actual_agents == expected_agents, f"Expected {expected_agents}, got {actual_agents}"

    # Check that agent names are uppercase
    for agent_name in agent_names:
        assert agent_name.isupper(), f"Agent name '{agent_name}' should be uppercase"

    # Check specific uppercase transformation
    expected_names = {"WEB", "AGENCY", "CALL_CENTER"}
    actual_names = set(agent_names)
    assert actual_names == expected_names, f"Expected {expected_names}, got {actual_names}"


def test_build_calculated_table_with_duplicates(spark):
    """Test deduplication across both tables."""

    # Create test data with duplicates across both tables
    reservations = spark.createDataFrame(
        [
            ("PNR001", "web"),
            ("PNR002", "agency"),
            ("PNR003", "web"),  # Duplicate in same table
            ("PNR004", "call_center"),
        ],
        schema=get_reservations_schema(),
    )

    reservation_segs = spark.createDataFrame(
        [
            ("PNR001", 1, "web"),  # Duplicate across tables
            ("PNR001", 2, "web"),  # Duplicate in same table
            ("PNR002", 1, "agency"),  # Duplicate across tables
            ("PNR003", 1, "mobile"),  # New agent
            ("PNR004", 1, "call_center"),  # Duplicate across tables
        ],
        schema=get_reservation_segs_schema(),
    )

    tables_dict = {"reservations": reservations, "reservation_segs": reservation_segs}

    result = build_calculated_table(spark, tables_dict)

    # Verify result
    assert result is not None, "Result should not be None"
    assert result.count() == 4, "Should have 4 distinct agents after deduplication"

    # Collect results and verify content
    result_rows = result.collect()
    agent_keys = [row["agent_key"] for row in result_rows]
    agent_names = [row["agent_name"] for row in result_rows]

    # Check that all expected agents are present
    expected_agents = {"web", "agency", "call_center", "mobile"}
    actual_agents = set(agent_keys)
    assert actual_agents == expected_agents, f"Expected {expected_agents}, got {actual_agents}"

    # Check that agent names are uppercase
    expected_names = {"WEB", "AGENCY", "CALL_CENTER", "MOBILE"}
    actual_names = set(agent_names)
    assert actual_names == expected_names, f"Expected {expected_names}, got {actual_names}"


def test_build_calculated_table_with_nulls(spark):
    """Test handling of null values."""

    # Create test data with null booking_agent values
    reservations = spark.createDataFrame(
        [
            ("PNR001", "web"),
            ("PNR002", None),  # Null value
            ("PNR003", "agency"),
        ],
        schema=get_reservations_schema(),
    )

    reservation_segs = spark.createDataFrame(
        [
            ("PNR001", 1, "web"),
            ("PNR002", 1, None),  # Null value
            ("PNR003", 1, "agency"),
            ("PNR004", 1, "mobile"),
        ],
        schema=get_reservation_segs_schema(),
    )

    tables_dict = {"reservations": reservations, "reservation_segs": reservation_segs}

    result = build_calculated_table(spark, tables_dict)

    # Verify result
    assert result is not None, "Result should not be None"
    assert result.count() == 3, "Should have 3 agents after filtering out nulls"

    # Collect results and verify content
    result_rows = result.collect()
    agent_keys = [row["agent_key"] for row in result_rows]

    # Check that null values are filtered out
    expected_agents = {"web", "agency", "mobile"}
    actual_agents = set(agent_keys)
    assert actual_agents == expected_agents, f"Expected {expected_agents}, got {actual_agents}"

    # Verify no null values in result
    for row in result_rows:
        assert row["agent_key"] is not None, "Agent key should not be null"
        assert row["agent_name"] is not None, "Agent name should not be null"


def test_build_calculated_table_case_transformation(spark):
    """Test uppercase transformation of agent names."""

    # Create test data with mixed case
    reservations = spark.createDataFrame(
        [
            ("PNR001", "web"),
            ("PNR002", "Agency"),
            ("PNR003", "CALL_CENTER"),
        ],
        schema=get_reservations_schema(),
    )

    reservation_segs = spark.createDataFrame(
        [
            ("PNR001", 1, "mobile"),
            ("PNR002", 1, "Phone"),
            ("PNR003", 1, "online_booking"),
        ],
        schema=get_reservation_segs_schema(),
    )

    tables_dict = {"reservations": reservations, "reservation_segs": reservation_segs}

    result = build_calculated_table(spark, tables_dict)

    # Verify result
    assert result is not None, "Result should not be None"
    assert result.count() == 6, "Should have 6 distinct agents"

    # Collect results and verify content
    result_rows = result.collect()

    # Create mapping of agent_key to agent_name
    key_to_name = {row["agent_key"]: row["agent_name"] for row in result_rows}

    # Verify uppercase transformation
    expected_mapping = {
        "web": "WEB",
        "Agency": "AGENCY",
        "CALL_CENTER": "CALL_CENTER",
        "mobile": "MOBILE",
        "Phone": "PHONE",
        "online_booking": "ONLINE_BOOKING",
    }

    assert key_to_name == expected_mapping, f"Expected {expected_mapping}, got {key_to_name}"


def test_build_calculated_table_empty_tables(spark):
    """Test behavior with empty tables."""

    # Create empty tables
    reservations = spark.createDataFrame(
        [],
        schema=get_reservations_schema(),
    )

    reservation_segs = spark.createDataFrame(
        [],
        schema=get_reservation_segs_schema(),
    )

    tables_dict = {"reservations": reservations, "reservation_segs": reservation_segs}

    result = build_calculated_table(spark, tables_dict)

    # Verify result
    assert result is not None, "Result should not be None"
    assert result.count() == 0, "Should have 0 agents for empty tables"


def test_build_calculated_table_one_table_empty(spark):
    """Test behavior when one table is empty."""

    # Create one empty table and one with data
    reservations = spark.createDataFrame(
        [],
        schema=get_reservations_schema(),
    )

    reservation_segs = spark.createDataFrame(
        [
            ("PNR001", 1, "web"),
            ("PNR002", 1, "agency"),
        ],
        schema=get_reservation_segs_schema(),
    )

    tables_dict = {"reservations": reservations, "reservation_segs": reservation_segs}

    result = build_calculated_table(spark, tables_dict)

    # Verify result
    assert result is not None, "Result should not be None"
    assert result.count() == 2, "Should have 2 agents from reservation_segs only"

    # Collect results and verify content
    result_rows = result.collect()
    agent_keys = [row["agent_key"] for row in result_rows]

    expected_agents = {"web", "agency"}
    actual_agents = set(agent_keys)
    assert actual_agents == expected_agents, f"Expected {expected_agents}, got {actual_agents}"


def test_build_calculated_table_schema_validation(spark):
    """Test that the result has the expected schema."""

    # Create simple test data
    reservations = spark.createDataFrame(
        [("PNR001", "web")],
        schema=get_reservations_schema(),
    )

    reservation_segs = spark.createDataFrame(
        [("PNR001", 1, "web")],
        schema=get_reservation_segs_schema(),
    )

    tables_dict = {"reservations": reservations, "reservation_segs": reservation_segs}

    result = build_calculated_table(spark, tables_dict)

    # Verify result schema
    assert result is not None, "Result should not be None"

    columns = result.columns
    assert "agent_key" in columns, "Result should have agent_key column"
    assert "agent_name" in columns, "Result should have agent_name column"
    assert len(columns) == 2, f"Result should have exactly 2 columns, got {len(columns)}"

    # Verify column types
    schema = result.schema
    agent_key_field = next(field for field in schema.fields if field.name == "agent_key")
    agent_name_field = next(field for field in schema.fields if field.name == "agent_name")

    assert str(agent_key_field.dataType) == "StringType()", "agent_key should be StringType"
    assert str(agent_name_field.dataType) == "StringType()", "agent_name should be StringType"
