import sys
import pytest
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType

from src.spark.databricks.silver.dims.dim_currency import build_calculated_table


@pytest.fixture(scope="session")
def spark():
    """Local SparkSession for pytest (no Databricks Connect)."""
    python_exec = sys.executable
    spark = (
        SparkSession.builder.master("local[2]")
        .appName("pytests")
        .config("spark.pyspark.python", python_exec)
        .config("spark.pyspark.driver.python", python_exec)
        .config("spark.sql.adaptive.enabled", "false")
        .config("spark.sql.adaptive.coalescePartitions.enabled", "false")
        .getOrCreate()
    )
    yield spark
    spark.stop()


def get_reservations_schema():
    """Schema for reservations table."""
    return StructType(
        [
            <PERSON>ruct<PERSON>ield("res_currency", StringType(), True),
        ]
    )


def get_charges_schema():
    """Schema for res_charges table."""
    return StructType(
        [
            StructField("currency_code", StringType(), True),
        ]
    )


def test_basic_functionality(spark):
    """Test basic functionality with simple data."""
    reservations = spark.createDataFrame(
        [
            ("USD",),
            ("EUR",),
            ("JPY",),
        ],
        schema=get_reservations_schema(),
    )
    charges = spark.createDataFrame([], schema=get_charges_schema())
    # no payments source
    tables_dict = {"reservations": reservations, "res_charges": charges}
    result = build_calculated_table(spark, tables_dict)

    assert result is not None
    codes = set(row["currency_code"] for row in result.collect())
    expected = {"USD", "EUR", "JPY"}
    assert codes == expected


def test_with_duplicates(spark):
    """Test deduplication across all sources."""
    reservations = spark.createDataFrame(
        [
            ("USD",),
            ("eur",),
            ("USD",),  # duplicate in same
        ],
        schema=get_reservations_schema(),
    )
    charges = spark.createDataFrame(
        [
            ("EUR",),
            ("GBP",),
            ("eur",),  # duplicate across case
        ],
        schema=get_charges_schema(),
    )
    # no payments source
    tables_dict = {"reservations": reservations, "res_charges": charges}
    result = build_calculated_table(spark, tables_dict)

    assert result is not None
    codes = set(row["currency_code"] for row in result.collect())
    expected = {"USD", "eur", "EUR", "GBP"}
    assert codes == expected


def test_filter_nulls(spark):
    """Test that nulls are filtered out."""
    reservations = spark.createDataFrame(
        [
            (None,),
            ("USD",),
        ],
        schema=get_reservations_schema(),
    )
    charges = spark.createDataFrame([(None,)], schema=get_charges_schema())
    # no payments source
    tables_dict = {"reservations": reservations, "res_charges": charges}
    result = build_calculated_table(spark, tables_dict)

    assert result is not None
    codes = set(row["currency_code"] for row in result.collect())
    assert codes == {"USD"}


def test_mixed_case_preserved(spark):
    """Test that mixed case currency codes are preserved distinct."""
    reservations = spark.createDataFrame(
        [
            ("Usd",),
        ],
        schema=get_reservations_schema(),
    )
    charges = spark.createDataFrame([("USD",), ("usd",)], schema=get_charges_schema())
    # no payments source
    tables_dict = {"reservations": reservations, "res_charges": charges}
    result = build_calculated_table(spark, tables_dict)

    codes = set(row["currency_code"] for row in result.collect())
    # case-sensitive distinctness
    assert codes == {"Usd", "USD", "usd"}


def test_schema_validation(spark):
    """Test that the result has the expected schema and types."""
    reservations = spark.createDataFrame([("USD",)], schema=get_reservations_schema())
    charges = spark.createDataFrame([("EUR",)], schema=get_charges_schema())
    # no payments source
    tables_dict = {"reservations": reservations, "res_charges": charges}
    result = build_calculated_table(spark, tables_dict)

    assert result is not None
    columns = result.columns
    assert columns == ["currency_code", "currency_name", "symbol"]

    schema = result.schema
    for field in schema.fields:
        assert str(field.dataType) == "StringType()"
