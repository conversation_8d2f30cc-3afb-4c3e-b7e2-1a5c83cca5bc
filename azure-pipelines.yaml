# CI/CD pipeline

trigger:
  - master
  - dev
  
pool:
  vmImage: ubuntu-22.04 

steps:
- script: |
    pip uninstall -y numpy
    pip install numpy<2
    if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
  displayName: 'Setup environment and install requirements'

- script: |
    python -m pip install black
    black --check --diff --line-length 120 src
  displayName: 'Check formatting (Black)'

- script: |
    sudo npm install -g azurite
    sudo mkdir azurite
    sudo azurite --silent --location azurite --debug azurite/debug.log &
  displayName: 'Install and Run Azurite'

- script: |
    python -m pip install --upgrade pip setuptools wheel packaging
  displayName: 'Upgrade pip and build tools'

- script: |
    cd $(System.DefaultWorkingDirectory)/src/spark
    python setup.py bdist_wheel
  displayName: "Make wheels"

- script: |
    pip install pytest pytest-azurepipelines pytest-cov
    sudo apt-get update && sudo apt-get install default-jdk -y
    pip install -r $(System.DefaultWorkingDirectory)/src/tests/test-requirements.txt
    export PYTHONPATH="$PYTHONPATH:.:$(System.DefaultWorkingDirectory)/src:$(System.DefaultWorkingDirectory)/src/spark"
    # look for any test files
    TESTS_FOUND=$(find tests -type f -name 'test_*.py' | wc -l)
    if [ "$TESTS_FOUND" -eq 0 ]; then
      echo "No tests found under tests/, skipping pytest."
      exit 0
    fi
    # run pytest and only swallow exit code 5 (no tests)
    pytest tests/ \
      --doctest-modules \
      --junitxml=junit/test-results.xml \
      --cov=. \
      --cov-report=xml \
      || {
        ec=$?
        if [ $ec -eq 5 ]; then
          echo "Pytest collected no tests (exit code 5); treating as success."
          exit 0
        else
          exit $ec
        fi
      }
  displayName: 'Run tests'
