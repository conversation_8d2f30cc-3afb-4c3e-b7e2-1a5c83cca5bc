trigger:
  branches:
    include:
      - main  # deployment branch
      - dev  # development branch

pool:
  vmImage: 'ubuntu-latest'

variables:
  ${{ if eq(variables['Build.SourceBranchName'], 'main') }}:
    DATABRICKS_BUNDLE_TARGET: 'prod'
  ${{ if ne(variables['Build.SourceBranchName'], 'main') }}:
    DATABRICKS_BUNDLE_TARGET: 'dev'

steps:
  - task: AzureCLI@2
    displayName: "Get Databricks AAD token"
    inputs:
      azureSubscription: 'sub-data-ai-arm'  # your service connection
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: |
        echo "Retrieving AAD token for Databricks..."
        TOKEN=$(az account get-access-token \
          --resource 2ff814a6-3304-4ab8-85cb-cd0e6f879c1d \
          --query accessToken -o tsv)
        echo "##vso[task.setvariable variable=DATABRICKS_TOKEN]$TOKEN"

  - script: |
      echo "Installing Databricks CLI..."
      curl -fsSL https://raw.githubusercontent.com/databricks/setup-cli/main/install.sh | sh
    displayName: "Install Databricks CLI"

  - script: |
      echo "Authenticating with Databricks CLI via environment token..."
      databricks auth status

      echo "Validating Databricks bundle..."
      databricks bundle validate -t $(DATABRICKS_BUNDLE_TARGET)

      echo "Deploying Databricks bundle to $(DATABRICKS_BUNDLE_TARGET)..."
      databricks bundle deploy -t $(DATABRICKS_BUNDLE_TARGET)
    displayName: "Deploy Databricks Bundle"
