{"name": "ReservationsSQLQueryUpsert", "properties": {"folder": {"name": "Utils"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "18f9fa9e-2434-48e8-b9ec-8afc088cca15"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "Synapse PySpark"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28, "automaticScaleJobs": false}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "source": ["from datetime import datetime, timedelta\n", "\n", "from pyspark.sql.functions import col\n", "from pyspark.sql.types import StringType, DateType, LongType, IntegerType, TimestampType, StructType, StructField\n", "from pyspark.sql import SparkSession\n", "from pyspark.sql.dataframe import DataFrame\n", "\n", "from delta.tables import DeltaTable"], "execution_count": 2}, {"cell_type": "code", "source": ["spark.conf.set(\"spark.sql.parquet.datetimeRebaseModeInWrite\", \"LEGACY\")\n", "spark.conf.set(\"spark.sql.parquet.int96RebaseModeInWrite\", \"LEGACY\")"], "execution_count": 3}, {"cell_type": "code", "source": ["yesterday = (datetime.now() - <PERSON><PERSON><PERSON>(days=1)).strftime('%Y-%m-%d')"], "execution_count": 4}, {"cell_type": "code", "metadata": {"collapsed": false}, "source": ["def run_sql_on_delta_files(spark: SparkSession) -> DataFrame:\n", "    \"\"\"\n", "    Creates temporary views on top of Delta files using direct paths and runs a raw SQL query,\n", "    but restricts cabin_lid to the max per (logical_flight_id, departure_date).\n", "    \"\"\"\n", "    # Define the base path to your Delta tables using the direct file system path\n", "    base_path = \"/lakehouse/delta/bronze/radixx\"\n", "\n", "    # --- 1. Create Temporary Views for each table ---\n", "    table_names = [\n", "        \"reservation_segs\", \"reservations\", \"flight_segments\",\n", "        \"fl_marketed_flights\", \"promotions\", \"res_seg_status\",\n", "        \"travel_agencies\", \"res_channels\", \"fare_class\",\n", "        \"seat_assignments\", \"cabin_lid\", \"contact_info\", \"person_org\"\n", "    ]\n", "\n", "    for table in table_names:\n", "        spark.read.format(\"delta\") \\\n", "            .load(f\"{base_path}/{table}\") \\\n", "            .createOrReplaceTempView(table)\n", "\n", "    print(\"Temporary views created successfully.\")\n", "\n", "    # --- 2. Define your SQL query with a CTE for cabin_max ---\n", "    sql_query = f\"\"\"\n", "    WITH cabin_max AS (\n", "      SELECT\n", "        logical_flight_id,\n", "        departure_date,\n", "        MAX(last_modified_date) AS last_modified_date,\n", "        CASE\n", "          WHEN SUM(cabin_lid) = 0    THEN 0\n", "          WHEN SUM(cabin_lid) >= 180 THEN 189\n", "          ELSE 165\n", "        END AS cabin_lid\n", "      FROM cabin_lid\n", "      GROUP BY logical_flight_id, departure_date\n", "    )\n", "    SELECT\n", "      rs.confirmation_num,\n", "      CAST(rs.book_date   + INTERVAL 2 HOURS AS DATE)       AS book_date_mod,\n", "      date_format(rs.book_date + INTERVAL 2 HOURS, 'HH:mm') AS book_time_mod,\n", "      concat(\n", "        hour(rs.book_date + INTERVAL 2 HOURS),\n", "        ' - ',\n", "        hour(rs.book_date + INTERVAL 3 HOURS)\n", "      ) AS book_hourly_mod,\n", "      rs.booking_agent              AS user_segment,\n", "      rs.crs_code                   AS gds_code,\n", "      rs.person_org_id,\n", "      rs.iata_num,\n", "      CASE WHEN rs.cancel_date IS NULL THEN 1 ELSE 0 END    AS cancel_flag,\n", "      CAST(rs.cancel_date   + INTERVAL 2 HOURS AS DATE)     AS cancel_date,\n", "      concat(rs.confirmation_num, rs.record_num)            AS segkey,\n", "      CASE WHEN rs.from_record_num >= 1 THEN 1 ELSE 0 END   AS flt_change,\n", "      rs.ptc_id                     AS pax_type,\n", "      concat(\n", "        hour(res.book_date + INTERVAL 2 HOURS),\n", "        ' - ',\n", "        hour(res.book_date + INTERVAL 3 HOURS)\n", "      ) AS book_hourly,\n", "      CAST(res.book_date + INTERVAL 2 HOURS AS DATE)      AS book_date,\n", "      date_format(res.book_date + INTERVAL 2 HOURS, 'HH:mm') AS book_time,\n", "\n", "      concat(fs.logical_flight_id, cast(fs.departure_date AS DATE)) AS flt_key,\n", "      fs.fare_class_code                  AS fare_class,\n", "      substring(fs.saved_fb_code, 2, 3)   AS fare_type,\n", "      fs.saved_fb_code                    AS fare_basis_code,\n", "      concat(cast(fs.departure_date AS DATE), fs.operating_flight_num) AS fn_key,\n", "      fs.operating_flight_num             AS flight_no,\n", "      concat(\n", "        substring(mf.flight_num, 3, 5), '-', mf.depart_station, '-', mf.arrive_station, '-', cast(mf.passenger_std_lt AS DATE)\n", "      ) AS infare_key,\n", "      mf.logical_flight_id,\n", "      mf.flight_status,\n", "      concat(hour(mf.passenger_std_lt), ' - ', hour(mf.passenger_std_lt) + 1) AS departure_hourly,\n", "      CAST(mf.passenger_std_lt AS DATE)   AS departure_date,\n", "      date_format(mf.passenger_std_lt, 'HH:mm') AS departure_time,\n", "      CAST(mf.passenger_sta_lt AS DATE)    AS arrival_date,\n", "      date_format(mf.passenger_sta_lt, 'HH:mm')  AS arrival_time,\n", "      mf.depart_station                 AS from_airport,\n", "      mf.arrive_station                 AS to_airport,\n", "      concat(mf.depart_station, ' - ', mf.arrive_station) AS route,\n", "\n", "      rss.res_seg_status_desc AS segment_status,\n", "      p.promotion_code        AS promo_code,\n", "      CASE WHEN p.promotion_code IS NULL THEN 0 ELSE 1 END AS promo_flag,\n", "      ta.legal_name           AS iata_name,\n", "      rch.res_channel         AS channel_segment,\n", "      fc.default_nest         AS fare_nesting,\n", "\n", "      sa.row_num               AS row_no,\n", "      sa.seat_column           AS seat,\n", "      sa.boarding_pass_num     AS boarding_pass,\n", "      sa.boarding_sequence,\n", "      sa.checkin_agent_username AS checkin_agent,\n", "\n", "      cl.cabin_lid             AS cabin_lid,\n", "\n", "      ci.contact_field         AS ID_Number,\n", "      po.date_of_birth,\n", "      CAST(datediff(current_date(), po.date_of_birth) / 365.25 AS INT) AS Age\n", "\n", "    FROM reservation_segs rs\n", "      JOIN reservations res\n", "        ON rs.confirmation_num = res.confirmation_num\n", "      JOIN flight_segments fs\n", "        ON rs.confirmation_num = fs.confirmation_num\n", "       AND rs.record_num = fs.record_num\n", "      JOIN fl_marketed_flights mf\n", "        ON fs.logical_flight_id = mf.logical_flight_id\n", "       AND fs.departure_date = mf.actual_depart_date_lt\n", "      LEFT JOIN promotions p\n", "        ON rs.promotion_id = p.promotion_id\n", "      LEFT JOIN res_seg_status rss\n", "        ON rs.res_seg_status = rss.res_seg_status\n", "      LEFT JOIN travel_agencies ta\n", "        ON rs.iata_num = ta.iata_num\n", "      LEFT JOIN res_channels rch\n", "        ON rch.res_channel_id = rs.res_channel_id\n", "      LEFT JOIN fare_class fc\n", "        ON fc.fare_class_code = fs.fare_class_code\n", "      LEFT JOIN seat_assignments sa\n", "        ON rs.confirmation_num = sa.confirmation_num\n", "       AND rs.record_num = sa.record_num\n", "      LEFT JOIN cabin_max cl\n", "        ON fs.logical_flight_id = cl.logical_flight_id\n", "       AND fs.departure_date = cl.departure_date\n", "      LEFT JOIN (\n", "        SELECT person_org_id, contact_field\n", "        FROM (\n", "          SELECT\n", "            person_org_id,\n", "            contact_field,\n", "            ROW_NUMBER() OVER (\n", "              PARTITION BY person_org_id\n", "              ORDER BY last_modified_date DESC\n", "            ) AS rn\n", "          FROM contact_info\n", "          WHERE contact_type = '6'\n", "        ) t\n", "        WHERE rn = 1\n", "      ) ci\n", "        ON rs.person_org_id = ci.person_org_id\n", "      LEFT JOIN person_org po\n", "        ON po.person_org_id = ci.person_org_id\n", "    WHERE \n", "      mf.last_modified_date >= '{yesterday}'\n", "      AND mf.flight_status NOT IN ('NOOP', 'CANCELED')\n", "    \"\"\"\n", "\n", "    # --- 3. Execute the SQL query and return the result as a DataFrame ---\n", "    final_dataframe = spark.sql(sql_query)\n", "    return final_dataframe\n", "\n", "# Usage:\n", "final_df = run_sql_on_delta_files(spark)"], "execution_count": 8}, {"cell_type": "code", "source": ["# Define the target schema including nullability\n", "target_schema = StructType([\n", "    StructField(\"confirmation_num\", StringType(), True),\n", "    StructField(\"book_date_mod\", DateType(), True),\n", "    StructField(\"book_time_mod\", StringType(), True),\n", "    StructField(\"book_hourly_mod\", StringType(), True),\n", "    StructField(\"user_segment\", StringType(), True),\n", "    StructField(\"gds_code\", StringType(), True),\n", "    StructField(\"person_org_id\", LongType(), True),\n", "    StructField(\"iata_num\", StringType(), True),\n", "    StructField(\"cancel_flag\", IntegerType(), True),\n", "    StructField(\"cancel_date\", DateType(), True),\n", "    StructField(\"segkey\", StringType(), True),\n", "    StructField(\"flt_change\", IntegerType(), True),\n", "    StructField(\"pax_type\", LongType(), True),\n", "    StructField(\"book_hourly\", StringType(), True),\n", "    StructField(\"book_date\", DateType(), True),\n", "    StructField(\"book_time\", StringType(), True),\n", "    StructField(\"flt_key\", StringType(), True),\n", "    StructField(\"fare_class\", LongType(), True),\n", "    StructField(\"fare_type\", StringType(), True),\n", "    StructField(\"fare_basis_code\", StringType(), True),\n", "    StructField(\"fn_key\", StringType(), True),\n", "    StructField(\"flight_no\", StringType(), True),\n", "    StructField(\"infare_key\", StringType(), True),\n", "    StructField(\"logical_flight_id\", LongType(), True),\n", "    StructField(\"flight_status\", StringType(), True),\n", "    StructField(\"departure_hourly\", StringType(), True),\n", "    StructField(\"departure_date\", DateType(), True),\n", "    StructField(\"departure_time\", StringType(), True),\n", "    StructField(\"arrival_date\", DateType(), True),\n", "    StructField(\"arrival_time\", StringType(), True),\n", "    StructField(\"from_airport\", StringType(), True),\n", "    StructField(\"to_airport\", StringType(), True),\n", "    StructField(\"route\", StringType(), True),\n", "    StructField(\"segment_status\", StringType(), True),\n", "    StructField(\"promo_code\", StringType(), True),\n", "    StructField(\"promo_flag\", IntegerType(), True),\n", "    StructField(\"iata_name\", StringType(), True),\n", "    StructField(\"channel_segment\", StringType(), True),\n", "    StructField(\"fare_nesting\", LongType(), True),\n", "    StructField(\"row_no\", StringType(), True),\n", "    StructField(\"seat\", StringType(), True),\n", "    StructField(\"boarding_pass\", LongType(), True),\n", "    StructField(\"boarding_sequence\", StringType(), True),\n", "    StructField(\"checkin_agent\", StringType(), True),\n", "    StructField(\"cabin_lid\", IntegerType(), True),\n", "    StructField(\"ID_Number\", StringType(), True),\n", "    StructField(\"date_of_birth\", TimestampType(), True),\n", "    <PERSON><PERSON>ct<PERSON>ield(\"Age\", IntegerType(), True)\n", "])\n", "\n", "\n", "casted_df = final_df.select([\n", "    col(field.name).cast(field.dataType).alias(field.name)\n", "    for field in target_schema.fields\n", "])\n", "\n", "final_df_conformed = spark.createDataFrame(casted_df.rdd, schema=target_schema)"], "execution_count": 18}, {"cell_type": "code", "source": ["existing_table = DeltaTable.forPath(spark, \"/lakehouse/delta/gold/ml_reservations\")"], "execution_count": 19}, {"cell_type": "code", "source": ["existing_table.alias(\"old\").merge(final_df_conformed.alias(\"new\"), \"old.segkey = new.segkey\").whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()"], "execution_count": 20}]}}