{"name": "CreateDatabricksJobsConfig", "properties": {"folder": {"name": "Configs"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "84c9d4c0-249a-4bf0-b341-9dccaae5153b"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "Synapse PySpark"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28, "automaticScaleJobs": false}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "source": ["import json\n", "\n", "# ── ENV CONFIG ─────────────────────────────────────────────────────────\n", "env            = spark.conf.get(\"spark.env\", \"dev\")\n", "delta_path     = \"/lakehouse/delta/configs/databricks_jobs\"\n", "\n", "if env == \"dev\":\n", "    cluster_id     = \"0703-124105-oa6zhuqc\"\n", "    databricks_url = \"https://adb-293568896631603.3.azuredatabricks.net\"\n", "elif env == \"prod\":\n", "    cluster_id     = \"0703-144730-damyif77\"\n", "    databricks_url = \"https://adb-2773699199994749.9.azuredatabricks.net\"\n", "else:\n", "    raise ValueError(f\"Unknown env: {env!r}\")\n", "\n", "# ── JOBS (only edit this!) ──────────────────────────────────────────────\n", "jobs_json = \"\"\"\n", "[\n", "  {\n", "    \"job_name\": \"UpdateUnityCatalog\",\n", "    \"job_id\": { \"dev\": 248226193417623, \"prod\": 111222333444555 }\n", "  },\n", "  {\n", "    \"job_name\": \"UpdateMetrics\",\n", "    \"job_id\": { \"dev\": 750917922960840, \"prod\": 666777888999000 }\n", "  },\n", "  {\n", "    \"job_name\": \"UpdateMLReservations\",\n", "    \"job_id\": { \"dev\": 760251170268939, \"prod\": 123123123123123 }\n", "  }\n", "]\n", "\"\"\"\n", "\n", "# ── WRITE FUNCTION ────────────────────────────────────\n", "def write_databricks_jobs(jobs, env, cluster_id, url, path):\n", "    rows = []\n", "    for j in jobs:\n", "        jid = j[\"job_id\"].get(env)\n", "        if jid is None:\n", "            raise KeyError(f\"Missing job_id for {j['job_name']} in env {env}\")\n", "        rows.append((env, j[\"job_name\"], cluster_id, jid, url))\n", "\n", "    cols = [\"env\",\"job_name\",\"cluster_id\",\"job_id\",\"databricks_url\"]\n", "    df   = spark.createDataFrame(rows, cols)\n", "\n", "    # write Delta\n", "    (df.write\n", "       .format(\"delta\")\n", "       .mode(\"overwrite\")\n", "       .option(\"overwriteSchema\", \"true\")\n", "       .save(path)\n", "    )\n", "    return df\n", "\n", "# ── RUN & CAPTURE ─────────────────────────────────────────────────────\n", "jobs = json.loads(jobs_json)\n", "df   = write_databricks_jobs(jobs, env, cluster_id, databricks_url, delta_path)\n", "\n", "# ── JSON OUTPUT ───────────────────────────────────────────────────────\n", "json_path = delta_path.replace(\"/delta/\", \"/json/\")\n", "(df.write\n", "   .mode(\"overwrite\")\n", "   .json(json_path)\n", ")"], "execution_count": 1}]}}