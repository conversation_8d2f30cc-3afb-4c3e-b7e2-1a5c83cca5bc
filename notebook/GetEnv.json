{"name": "GetEnv", "properties": {"folder": {"name": "Utils"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "81f30349-81c1-4e12-94bb-888443dd98e6"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "python"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "source": ["from notebookutils import mssparkutils"], "execution_count": null}, {"cell_type": "code", "source": ["env = spark.conf.get(\"spark.env\")"], "execution_count": null}, {"cell_type": "code", "source": ["mssparkutils.notebook.exit(env)"], "execution_count": null}]}}