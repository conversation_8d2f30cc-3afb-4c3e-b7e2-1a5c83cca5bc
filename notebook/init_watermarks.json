{"name": "init_watermarks", "properties": {"folder": {"name": "<PERSON><PERSON><PERSON>"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "db32ca0a-beae-4f27-aebb-cec8e2efc494"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "Synapse PySpark"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28, "automaticScaleJobs": false}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "metadata": {"microsoft": {"language": "sparksql"}, "collapsed": false}, "source": ["%%sql\n", "CREATE DATABASE IF NOT EXISTS metadata"], "execution_count": 2}, {"cell_type": "code", "source": ["# Synapse / Databricks Notebook: Seed watermark Delta with PK + Watermark Column Info\n", "# ------------------------------------------------------------------------------------\n", "# 1) Imports & parameters\n", "from pyspark.sql.types import StructType, StructField, StringType, TimestampType, ArrayType\n", "from delta.tables       import DeltaTable\n", "import datetime\n", "\n", "metadata_path = \"/lakehouse/delta/metadata/watermarks\"\n", "baseline_dt   = datetime.datetime(1900, 1, 1)\n", "\n", "# 2) Define the Delta schema (including PK columns list and watermark column name)\n", "schema = StructType([\n", "    StructField(\"table_name\",                StringType(),  False),\n", "    StructField(\"primary_key_columns\",       ArrayType(StringType()), False),\n", "    StructField(\"watermark_column_name\",     StringType(),  False),\n", "    StructField(\"last_watermark\",            TimestampType(), False),\n", "    StructField(\"updated_at\",                TimestampType(), False),\n", "])\n", "\n", "# 3) Create the Delta if it doesn’t exist\n", "if not DeltaTable.isDeltaTable(spark, metadata_path):\n", "    spark.createDataFrame([], schema) \\\n", "         .write.format(\"delta\") \\\n", "         .mode(\"overwrite\") \\\n", "         .save(metadata_path)\n", "\n", "# 4) Read existing metadata\n", "df_meta = spark.read.format(\"delta\").load(metadata_path)\n", "\n", "# 5) Seed initial rows if table is empty\n", "if df_meta.rdd.isEmpty():\n", "    initial_watermarks = [\n", "        # table_name              primary_key_columns                                          watermark_column_name    last_wm        updated_at\n", "        (\"reservation_segs\",     [\"series_num\", \"confirmation_num\", \"record_num\"],             \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"flight_segments\",      [\"SERIES_NUM\", \"CONFIRMATION_NUM\", \"RECORD_NUM\"],             \"LAST_MODIFIED_DATE\",    baseline_dt,   baseline_dt),\n", "        (\"reservations\",         [\"series_num\", \"confirmation_num\"],                           \"last_modified\",         baseline_dt,   baseline_dt),\n", "        (\"fl_marketed_flights\",  [\"logical_flight_id\"],                                        \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"promotions\",           [\"promotion_id\"],                                             \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"res_seg_status\",       [\"res_seg_status\"],                                           \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"res_channels\",         [\"res_channel_id\"],                                           \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"seat_assignments\",     [\"series_num\", \"confirmation_num\", \"record_num\", \"leg_order\"],\"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"res_charges\",          [\"res_charge_id\"],                                            \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"cabin_lid\",            [\"logical_flight_id\", \"departure_date\", \"cabin\"],             \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"res_payment_map\",      [\"res_payment_id\", \"res_charge_id\"],                          \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"travel_agencies\"),     [\"iata_num\"],                                                 \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"fare_class\"),          [\"fare_class_code\"],                                          \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"contact_info\"),        [\"contact_id\"],                                               \"last_modified_date\",    baseline_dt,   baseline_dt),\n", "        (\"person_org\"),          [\"person_org_id\"],                                            \"last_modified_date\",    baseline_dt,   baseline_dt), \n", "    ]\n", "    spark.createDataFrame(initial_watermarks, schema) \\\n", "         .write.format(\"delta\") \\\n", "         .mode(\"overwrite\") \\\n", "         .save(metadata_path)"], "execution_count": 2}, {"cell_type": "code", "metadata": {"microsoft": {"language": "sparksql"}, "collapsed": false}, "source": ["%%sql\n", "CREATE TABLE IF NOT EXISTS metadata.watermarks\n", "USING DELTA \n", "LOCATION \"/lakehouse/delta/metadata/watermarks\""], "execution_count": 3}]}}