{"name": "Reservations", "properties": {"folder": {"name": "Debugging"}, "nbformat": 4, "nbformat_minor": 2, "bigDataPool": {"referenceName": "lakeshousespark", "type": "BigDataPoolReference"}, "sessionProperties": {"driverMemory": "28g", "driverCores": 4, "executorMemory": "28g", "executorCores": 4, "numExecutors": 2, "conf": {"spark.dynamicAllocation.enabled": "false", "spark.dynamicAllocation.minExecutors": "2", "spark.dynamicAllocation.maxExecutors": "2", "spark.autotune.trackingId": "755ad184-4013-4017-9d2a-60e190fbace4"}}, "metadata": {"saveOutput": true, "enableDebugMode": false, "kernelspec": {"name": "synapse_pyspark", "display_name": "Synapse PySpark"}, "language_info": {"name": "python"}, "a365ComputeOptions": {"id": "/subscriptions/a8dd0f75-83fe-44a1-8061-7666d175ba4b/resourceGroups/dev-san-rg-lakehouse-001/providers/Microsoft.Synapse/workspaces/dev-san-syn-lakehouse-001/bigDataPools/lakeshousespark", "name": "lakeshousespark", "type": "Spark", "endpoint": "https://dev-san-syn-lakehouse-001.dev.azuresynapse.net/livyApi/versions/2019-11-01-preview/sparkPools/lakeshousespark", "auth": {"type": "AAD", "authResource": "https://dev.azuresynapse.net"}, "sparkVersion": "3.4", "nodeCount": 10, "cores": 4, "memory": 28, "automaticScaleJobs": false}, "sessionKeepAliveTimeout": 30}, "cells": [{"cell_type": "code", "source": ["# Cell 1 — load all bronze tables “pure”\n", "df_reservation_segs   = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/reservation_segs\")\n", "df_reservations       = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/reservations\")\n", "df_flight_segments    = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/flight_segments\")\n", "df_fl_marketed_flights= spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/fl_marketed_flights\")\n", "df_promotions         = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/promotions\")\n", "df_res_seg_status     = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/res_seg_status\")\n", "df_travel_agencies    = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/travel_agencies\")\n", "df_res_channels       = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/res_channels\")\n", "df_fare_class         = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/fare_class\")\n", "df_seat_assignments   = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/seat_assignments\")\n", "df_vwCabin            = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/cabin_lid\")\n", "df_contact_info       = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/contact_info\")\n", "df_person_org         = spark.read.format(\"delta\").load(\"/lakehouse/delta/bronze/radixx/person_org\")\n", ""], "execution_count": 47}, {"cell_type": "code", "source": ["df_contact_info = df_contact_info.filter(<PERSON><PERSON>col(\"contact_type\") == 6)"], "execution_count": 50}, {"cell_type": "code", "source": ["import pyspark.sql.functions as F"], "execution_count": 6}, {"cell_type": "code", "source": ["from pyspark.sql.window import Window\n", "\n", "window = Window.partitionBy(\"logical_flight_id\", \"departure_date\").orderBy(F.desc(\"cabin_lid\"))\n", "df_vwCabin = df_vwCabin.withColumn(\"rank\", F.row_number().over(window)).filter(F.col(\"rank\") == 1).drop(\"rank\")"], "execution_count": 52}, {"cell_type": "code", "source": ["import pandas as pd\n", "\n", "# show all columns\n", "pd.set_option('display.max_columns', None)\n", "# expand notebook width so it doesn’t wrap\n", "pd.set_option('display.width', None)\n", ""], "execution_count": 9}, {"cell_type": "code", "source": ["df = df_reservations.filter(<PERSON><PERSON>col(\"confirmation_num\") == 'QR9FF0')"], "execution_count": 53}, {"cell_type": "code", "source": ["df = df_reservation_segs.join(df,\n", "                              on=\"confirmation_num\", how=\"inner\")"], "execution_count": 54}, {"cell_type": "code", "source": ["df = df.join(df_flight_segments,\n", "             on=[\"confirmation_num\",\"record_num\"], how=\"inner\")"], "execution_count": 55}, {"cell_type": "code", "source": ["df = df.join(df_fl_marketed_flights,\n", "             (df.LOGICAL_FLIGHT_ID == df_fl_marketed_flights.logical_flight_id) &\n", "             (df.DEPARTURE_DATE   == df_fl_marketed_flights.actual_depart_date_lt),\n", "             how=\"inner\")"], "execution_count": 56}, {"cell_type": "code", "source": ["df = df.join(df_promotions,\n", "             on=\"promotion_id\", how=\"left\")"], "execution_count": 57}, {"cell_type": "code", "source": ["df = df.join(df_res_seg_status,\n", "             on=\"res_seg_status\", how=\"left\")"], "execution_count": 58}, {"cell_type": "code", "source": ["df = df.join(df_travel_agencies,\n", "             on=\"iata_num\", how=\"left\")"], "execution_count": 59}, {"cell_type": "code", "source": ["df = df.join(df_res_channels,\n", "             on=\"res_channel_id\", how=\"left\")"], "execution_count": 60}, {"cell_type": "code", "source": ["df = df.join(df_fare_class,\n", "             on=\"fare_class_code\", how=\"left\")"], "execution_count": 61}, {"cell_type": "code", "source": ["df = df.join(df_seat_assignments,\n", "             on=[\"confirmation_num\",\"record_num\"], how=\"left\")"], "execution_count": 62}, {"cell_type": "code", "source": ["df = df.join(df_vwCabin,\n", "             on=[\"logical_flight_id\",\"departure_date\"], how=\"left\")"], "execution_count": 63}, {"cell_type": "code", "source": ["df = df.join(df_contact_info,\n", "             on=\"person_org_id\", how=\"left\")"], "execution_count": 65}, {"cell_type": "code", "source": ["df = df.join(df_person_org,\n", "             on=\"person_org_id\", how=\"left\")\n", ""], "execution_count": 67}, {"cell_type": "code", "metadata": {"collapsed": false}, "source": ["display(df)"], "execution_count": 69}, {"cell_type": "code", "source": ["df.count()"], "execution_count": 68}]}}