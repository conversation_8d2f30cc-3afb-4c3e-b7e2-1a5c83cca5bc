# 0.2.0 (22 July 2025)

## Release highlights
- Full parameterization off all services and pipelines
- New lake databases added
- Daily orchestation now updates from bronze to the ml_reservations gold table (skipping silver)
- Initial silver jobs added


# 0.1.0 (04 July 2025)

## Release highlights
- Linked service created to Cipherwave Radixx using Cipherwave VM-hosted SHIR
- Added Ingestion Pipeline for Radixx reservations-related tables
- Created watermarking incremental ingestion
- Added Lakedatabase creation job